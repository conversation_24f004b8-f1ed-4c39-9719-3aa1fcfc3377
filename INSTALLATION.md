# Installation guide

# Frontend

## Create .env 

```
VITE_API_URL=http://localhost:8000 #Or in our case https://api-agent.ii.inc
VITE_GOOGLE_CLIENT_ID=
```

## Package installation
```
bun install
bun run build
```

## Start script

```
bun run preview
```


# Backend

## Installation script and change environment (Same with conda)

```
uv venv --python 3.10
uv pip install -e . --prerelease=allow
source .venv/bin/activate
```

## Start script

Copy from /home/<USER>/ii-agent-prod/test/ii-agent-prod/start.sh

and run 

```
bash start.sh
```

## start.sh

```
#AUTH
export GOOGLE_CLIENT_ID=
export GOOGLE_CLIENT_SECRET=
export OAUTHLIB_INSECURE_TRANSPORT=1
export GOOGLE_DISCOVERY_URL=
export ACCESS_TOKEN_EXPIRE_MINUTES=

#LLM CONFIG
export LLM_CONFIGS='{"gemini/gemini-2.5-pro":{"model":"gemini-2.5-pro","api_key":"FILL IN API KEY","base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"vertex/claude-sonnet-4@20250514":{"model":"claude-sonnet-4@20250514","api_key":null,"base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":"us-east5","vertex_project_id":"backend-alpha-97077","api_type":"anthropic","thinking_tokens":10000,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"default":{"model":"gemini-2.5-pro","api_key":"FILL IN API KEY","base_url":null,"max_retries":10,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false}}'

#TOOL CONFIG
export AUTO_APPROVE_TOOLS=true
#IMAGE
export IMAGE_SEARCH_SERPAPI_API_KEY=
#SEARCH
export WEB_SEARCH_SERPAPI_API_KEY=
export WEB_VISIT_FIRECRAWL_API_KEY=
#DATABASE
export DATABASE_NEON_DB_API_KEY=
#SANDBOX AND LIFECYCLE
export SANDBOX_E2B_API_KEY=
export SANDBOX_E2B_TEMPLATE_ID=
export SANDBOX_REDIS_URL=

#Application Database. Local for now. But can get a cloud connection later
export DATABASE_URL="sqlite+aiosqlite:///./ii_agent.db"
export TOOL_SERVER_URL="http://*************:1235"
export SANDBOX_SERVER_URL="http://*************:8100"
export STRIPE_SECRET_KEY=
export STRIPE_PRICE_PLUS_MONTHLY=
export STRIPE_PRICE_PLUS_ANNUALLY=
export STRIPE_PRICE_PRO_MONTHLY=
export STRIPE_PRICE_PRO_ANNUALLY=
export STRIPE_RETURN_URL=
export STRIPE_SUCCESS_URL=
export STRIPE_CANCEL_URL=
export STRIPE_WEBHOOK_SECRET=


python ws_server.py --port 9999

```



# II-Tool-Server (Optional)
Handle 3rd party apikeys

## Run Script

```
export IMAGE_SEARCH_SERPAPI_API_KEY=

# Web Visit
export WEB_VISIT_FIRECRAWL_API_KEY=
# Video Generate
export VIDEO_GENERATE_GCP_PROJECT_ID=
export VIDEO_GENERATE_GCP_LOCATION=
export VIDEO_GENERATE_GCS_OUTPUT_BUCKET=

# Image Generate
export IMAGE_GENERATE_GCP_PROJECT_ID=
export IMAGE_GENERATE_GCP_LOCATION=
export IMAGE_GENERATE_GCS_OUTPUT_BUCKET=

# Database
export DATABASE_NEON_DB_API_KEY=
# Enhance Prompt
export ENHANCE_PROMPT_OPENAI_API_KEY=

# Compressor
export COMPRESSOR_COMPRESS_TYPE='["llm"]'
export COMPRESSOR_LLM_CONFIG='{
  "model": "gemini-lite",
  "base_url": "http://localhost:4000/",
  "api_key": "dummy"
}'


# Storage Provider
export STORAGE_PROVIDER="gcs"
export GCS_BUCKET_NAME="ii-agent-public"
export GCS_PROJECT_ID="backend-alpha-97077"

python -m src.ii_tool.integrations.app.main --port 1234
```

# Sandbox Server Run Script:

```
#!/bin/bash

# II Sandbox Server Startup Script
# This script starts the standalone sandbox server

set -e

# Default configuration
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8100"
DEFAULT_PROVIDER="e2b"
DEFAULT_REDIS_URL=

export E2B_API_KEY=
export E2B_TEMPLATE_ID=

# Allow overriding via environment variables
export SERVER_HOST="${SERVER_HOST:-$DEFAULT_HOST}"
export SERVER_PORT="${SERVER_PORT:-$DEFAULT_PORT}"
export PROVIDER="${PROVIDER:-$DEFAULT_PROVIDER}"
export REDIS_URL="${REDIS_URL:-$DEFAULT_REDIS_URL}"

export MCP_PORT="${MCP_PORT:-5173}"

# Timeout configuration
export TIMEOUT_SECONDS="${TIMEOUT_SECONDS:-5400}"
export PAUSE_BEFORE_TIMEOUT_SECONDS="${PAUSE_BEFORE_TIMEOUT_SECONDS:-600}"
export TIMEOUT_BUFFER_SECONDS="${TIMEOUT_BUFFER_SECONDS:-300}"

echo "Starting II Sandbox Server..."
echo "Host: $SERVER_HOST"
echo "Port: $SERVER_PORT"
echo "Provider: $PROVIDER"
echo "Redis URL: $REDIS_URL"

# Check if E2B API key is set when using E2B provider
if [ "$PROVIDER" = "e2b" ] && [ -z "$E2B_API_KEY" ]; then
  echo "Error: E2B_API_KEY environment variable is required when using E2B provider"
  exit 1
fi

# Check if Redis is accessible
echo "Checking Redis connection..."
if command -v redis-cli >/dev/null 2>&1; then
  if ! redis-cli -u "$REDIS_URL" ping >/dev/null 2>&1; then
    echo "Warning: Cannot connect to Redis at $REDIS_URL"
    echo "Make sure Redis is running or update REDIS_URL environment variable"
  else
    echo "Redis connection OK"
  fi
else
  echo "Warning: redis-cli not found, skipping Redis connection check"
fi

# Start the server using uvicorn
echo "Starting server..."
exec uvicorn ii_sandbox_server.main:app \
  --host "$SERVER_HOST" \
  --port "$SERVER_PORT" \
  --reload \
  --log-level info
```

# Check current alembic version 
- `uv run alembic -c src/ii_agent/alembic.ini current`