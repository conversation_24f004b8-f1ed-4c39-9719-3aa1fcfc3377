#!/usr/bin/env python3
"""
Seed slide_templates table from CSV file.
Usage: python scripts/seed_template.py [input_file]
"""

import asyncio
import csv
import os
import sys
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

csv.field_size_limit(sys.maxsize)

def parse_datetime_safe(value):
    from datetime import datetime
    if not value:
        return None
    try:
        return datetime.fromisoformat(value.replace("Z", "+00:00"))
    except Exception:
        return None

async def seed_templates_from_csv(database_url: str, input_file: str):
    """Seed slide_templates table from CSV file."""

    if not os.path.exists(input_file):
        print(f"Error: File {input_file} not found")
        sys.exit(1)

    # Create async engine
    engine = create_async_engine(database_url, echo=False)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    try:
        async with async_session() as session:
            # Read CSV file
            with open(input_file, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                templates = list(reader)

            if not templates:
                print("No templates found in CSV file.")
                return

            print(f"Found {len(templates)} templates in CSV file.")

            # Insert templates
            inserted_count = 0
            updated_count = 0
            skipped_count = 0

            for template in templates:
                template_id = template['id']
                slide_template_name = template['slide_template_name']
                slide_content = template['slide_content']

                # Convert pipe-separated string back to array
                images_str = template['slide_template_images']
                slide_template_images = images_str.split('|') if images_str else None

                # Parse timestamps or use None
                created_at = parse_datetime_safe(template['created_at']) if template['created_at'] else None
                updated_at = parse_datetime_safe(template['updated_at']) if template['updated_at'] else None

                try:
                    # Check if template already exists
                    check_query = text("""
                        SELECT id FROM slide_templates WHERE id = :id
                    """)
                    result = await session.execute(check_query, {"id": template_id})
                    existing = result.fetchone()

                    if existing:
                        # Update existing template
                        update_query = text("""
                            UPDATE slide_templates
                            SET slide_template_name = :name,
                                slide_content = :content,
                                slide_template_images = :images,
                                updated_at = COALESCE(CAST(:updated_at AS TIMESTAMP WITH TIME ZONE), NOW())
                            WHERE id = :id
                        """)
                        await session.execute(
                            update_query,
                            {
                                "id": template_id,
                                "name": slide_template_name,
                                "content": slide_content,
                                "images": slide_template_images,
                                "updated_at": updated_at
                            }
                        )
                        updated_count += 1
                        print(f"Updated template: {slide_template_name} (ID: {template_id})")
                    else:
                        # Insert new template
                        insert_query = text("""
                            INSERT INTO slide_templates
                            (id, slide_template_name, slide_content, slide_template_images, created_at, updated_at)
                            VALUES (:id, :name, :content, :images,
                                    COALESCE(CAST(:created_at AS TIMESTAMP WITH TIME ZONE), NOW()),
                                    CAST(:updated_at AS TIMESTAMP WITH TIME ZONE))
                        """)
                        await session.execute(
                            insert_query,
                            {
                                "id": template_id,
                                "name": slide_template_name,
                                "content": slide_content,
                                "images": slide_template_images,
                                "created_at": created_at,
                                "updated_at": updated_at
                            }
                        )
                        inserted_count += 1
                        print(f"Inserted template: {slide_template_name} (ID: {template_id})")

                except Exception as e:
                    print(f"Error processing template {template_id}: {e}")
                    skipped_count += 1

            # Commit transaction
            await session.commit()

            print(f"\nSeeding completed:")
            print(f"  - Inserted: {inserted_count}")
            print(f"  - Updated: {updated_count}")
            print(f"  - Skipped: {skipped_count}")

    except Exception as e:
        print(f"Error seeding templates: {e}")
        raise
    finally:
        await engine.dispose()


def main():
    # Get database URL from environment
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("Error: DATABASE_URL environment variable is not set")
        sys.exit(1)

    # Get input file from command line or use default
    input_file = sys.argv[1] if len(sys.argv) > 1 else 'templates.csv'

    print(f"Seeding templates from {input_file} to database...")

    # Run the seeding
    asyncio.run(seed_templates_from_csv(database_url, input_file))


if __name__ == '__main__':
    main()
