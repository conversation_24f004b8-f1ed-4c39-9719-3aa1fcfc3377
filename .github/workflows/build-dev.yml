name: Build and Push Docker Images (Development)

on:
  workflow_dispatch: {}
  push:
    branches:
      - develop

permissions:
  contents: read

env:
  REGION: us-central1
  REGISTRY: us-central1-docker.pkg.dev
  REPO_PATH: backend-alpha-97077/iirepo

jobs:
  build-and-push-development:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set image tag
        id: image_tag
        run: |
          # Development always uses commit hash
          echo "tag=${{ github.sha }}" >> $GITHUB_OUTPUT
          echo "Using commit hash: ${{ github.sha }}"

      - name: Authenticate to Google Cloud (Service Account Key)
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up gcloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker $REGISTRY --quiet

      - name: Build frontend image (development)
        run: |
          docker build \
            --build-arg BUILD_MODE=development \
            -t $REGISTRY/$REPO_PATH/ii-agent-dev-fe:${{ steps.image_tag.outputs.tag }} \
            -t $REGISTRY/$REPO_PATH/ii-agent-dev-fe:latest \
            -f ./docker/frontend/Dockerfile .

      - name: Push frontend image (development)
        run: |
          docker push $REGISTRY/$REPO_PATH/ii-agent-dev-fe:${{ steps.image_tag.outputs.tag }}
          docker push $REGISTRY/$REPO_PATH/ii-agent-dev-fe:latest

      - name: Build backend image (development)
        run: |
          docker build \
            -t $REGISTRY/$REPO_PATH/ii-agent-dev:${{ steps.image_tag.outputs.tag }} \
            -t $REGISTRY/$REPO_PATH/ii-agent-dev:latest \
            -f ./docker/backend/Dockerfile .

      - name: Push backend image (development)
        run: |
          docker push $REGISTRY/$REPO_PATH/ii-agent-dev:${{ steps.image_tag.outputs.tag }}
          docker push $REGISTRY/$REPO_PATH/ii-agent-dev:latest

      - name: Output build information
        run: |
          echo "Environment: Development"
          echo "Image Tag: ${{ steps.image_tag.outputs.tag }}"
          echo "Branch: ${{ github.ref }}"
