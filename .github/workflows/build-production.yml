name: Build and Push Docker Images (Production)

on:
  workflow_dispatch: {}
  push:
    tags:
      - 'release-*'

permissions:
  contents: read

env:
  REGION: us-central1
  REGISTRY: us-central1-docker.pkg.dev
  REPO_PATH: backend-alpha-97077/iirepo

jobs:
  build-and-push-production:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Extract tag name
        id: image_tag
        run: |
          # Extract full tag name (refs/tags/release-0.1.0 -> release-0.1.0)
          TAG=${GITHUB_REF#refs/tags/}
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          echo "Using release tag: $TAG"

      - name: Authenticate to Google Cloud (Service Account Key)
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up gcloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker $REGISTRY --quiet

      - name: Build frontend image (production)
        run: |
          docker build \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod-fe:${{ steps.image_tag.outputs.tag }} \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod-fe:latest \
            -f ./docker/frontend/Dockerfile .

      - name: Push frontend image (production)
        run: |
          docker push $REGISTRY/$REPO_PATH/ii-agent-prod-fe:${{ steps.image_tag.outputs.tag }}
          docker push $REGISTRY/$REPO_PATH/ii-agent-prod-fe:latest

      - name: Build backend image (production)
        run: |
          docker build \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod:${{ steps.image_tag.outputs.tag }} \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod:latest \
            -f ./docker/backend/Dockerfile .

      - name: Push backend image (production)
        run: |
          docker push $REGISTRY/$REPO_PATH/ii-agent-prod:${{ steps.image_tag.outputs.tag }}
          docker push $REGISTRY/$REPO_PATH/ii-agent-prod:latest

      - name: Output build information
        run: |
          echo "Environment: Production"
          echo "Release Tag: ${{ steps.image_tag.outputs.tag }}"
          echo "Git Ref: ${{ github.ref }}"
          echo "Images built:"
          echo "  - $REGISTRY/$REPO_PATH/ii-agent-prod-fe:${{ steps.image_tag.outputs.tag }}"
          echo "  - $REGISTRY/$REPO_PATH/ii-agent-prod-fe:latest"
          echo "  - $REGISTRY/$REPO_PATH/ii-agent-prod:${{ steps.image_tag.outputs.tag }}"
          echo "  - $REGISTRY/$REPO_PATH/ii-agent-prod:latest"
