@import 'tailwindcss';

/* @import './satoshi.css'; */
@import './github-markdown.css';
@import './animations.css';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --color-mist: #d5dce0;
    --color-sky-blue: #bee6f0;
    --color-sky-blue-2: #a6ffff;
    --color-sky-blue-3: #bae9f4;
    --color-sky-blue-4: #87e7ff;
    --color-violet: #632af5;
    --color-pewter: #919eae;
    --color-slate: #56696d;
    --color-charcoal: #181e1c;
    --color-sidebar-border: rgba(229, 231, 235, 0.3);
    --color-black: #212121;
    --color-yellow: #ffde8a;
    --color-green: #006600;
    --color-grey: #e5e7eb;
    --color-grey-2: #c7c7c7;
    --color-grey-3: #fbfbfb;
    --color-firefly: #0f2b33;
    --color-red: #ff6b75;
    --color-red-2: #f54260;
    --shadow-btn: 0 4px 24px rgba(0, 0, 0, 0.16);
}

@keyframes breathing-fill {
    0%,
    100% {
        background-color: theme(--color-firefly);
    }
    50% {
        background-color: theme(--color-firefly / 0.6);
    }
}

@keyframes breathing-fill-dark {
    0%,
    100% {
        background-color: theme(--color-sky-blue);
    }
    50% {
        background-color: theme(--color-sky-blue / 0.6);
    }
}

.animate-breathing-fill {
    animation: breathing-fill 2s ease-in-out infinite;
}

.dark .animate-breathing-fill {
    animation: breathing-fill-dark 2s ease-in-out infinite;
}

@layer base {
    html {
        @apply h-full overscroll-none;
    }
    /* body {
        font-family:
            -apple-system,
            BlinkMacSystemFont,
            Segoe UI Variable Display,
            Segoe UI,
            Helvetica,
            Apple Color Emoji,
            Arial,
            sans-serif,
            Segoe UI Emoji,
            Segoe UI Symbol;
    } */
    body {
        font-family: 'Inter', sans-serif;
        font-optical-sizing: auto;
    }
    html.dark body {
        @apply bg-charcoal;
    }
    html.light body {
        @apply bg-white text-black;
    }

    html.light .monaco-editor {
        --vscode-editorGutter-background: #e5e7eb !important;
        --vscode-editor-background: #e5e7eb !important;
        --vscode-editorStickyScroll-background: #e5e7eb !important;
    }

    /* Allow text selection in message content and code blocks */
    .markdown-body,
    .markdown-body *,
    pre,
    pre *,
    code,
    code *,
    .select-text,
    .select-text * {
        @apply select-text;
    }

    .markdown-body pre > code {
        @apply !whitespace-break-spaces md:whitespace-pre;
    }

    .markdown-body table {
        @apply !w-[calc(100vw-72px)] md:w-full;
    }

    .markdown-body a {
        @apply break-all;
    }

    /* Keep UI elements non-selectable */
    button,
    nav,
    .sidebar,
    .toolbar {
        @apply select-none;
    }

    .bg-blue-gradient {
        background: #bae9f4;
        background: linear-gradient(
            90deg,
            rgba(120, 200, 210, 1) 0%,
            rgba(186, 233, 244, 1) 50%
        );
    }

    .bg-black-gradient {
        background: linear-gradient(
            180deg,
            rgba(24, 30, 28, 0.8) 0%,
            rgba(24, 30, 28, 0) 40%,
            rgba(24, 30, 28, 0) 70%,
            rgba(24, 30, 28, 0.8) 100%
        );
    }

    .bg-white-gradient {
        background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.8) 0%,
            rgba(255, 255, 255, 0) 40%,
            rgba(255, 255, 255, 0) 70%,
            rgba(255, 255, 255, 0.8) 100%
        );
    }

    html.dark .bg-white-gradient {
        background: linear-gradient(
            180deg,
            rgba(24, 30, 28, 0.8) 0%,
            rgba(24, 30, 28, 0) 40%,
            rgba(24, 30, 28, 0) 70%,
            rgba(24, 30, 28, 0.8) 100%
        );
    }

    .terminal {
        @apply h-full p-4;
    }

    .terminal .xterm-viewport {
        @apply !h-[325px];
    }

    .animate-spin {
        animation-duration: 800ms !important;
    }

    .xterm-screen {
        @apply !w-full !h-full;
    }

    .xterm-rows div {
        @apply !w-full;
    }

    [data-slide-id] {
        width: 100% !important;
    }
}

div[data-code-block-container] {
    @apply w-full max-w-[calc(100vw-24px)];
}
