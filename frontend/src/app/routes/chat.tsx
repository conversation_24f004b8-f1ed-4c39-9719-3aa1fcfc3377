import { useCallback, useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate, useSearchParams } from 'react-router'

import <PERSON>Header from '@/components/header'
import ModelTag from '@/components/model-tag'
import QuestionInput from '@/components/question-input'
import RightSidebar from '@/components/right-sidebar'
import { useChat } from '@/hooks/use-chat-query'
import {
    selectIsLoading,
    useAppSelector,
    useAppDispatch,
    setQuestionMode
} from '@/state'
import ThinkingMessage from '@/components/thinking-message'
import ChatMessageContent from '@/components/chat-message-content'
import ButtonIcon from '@/components/button-icon'
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui/tooltip'
import AgentSetting from '@/components/agent-setting'
import Sidebar from '@/components/sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'
import { QUESTION_MODE } from '@/typings/agent'
import {
    Conversation,
    ConversationContent,
    ConversationScrollButton
} from '@/components/ai-elements/conversation'

export function ChatPage() {
    const [searchParams, setSearchParams] = useSearchParams()
    const initialSessionId = searchParams.get('id')

    const location = useLocation()
    const navigateTo = useNavigate()
    const [isOpenSetting, setIsOpenSetting] = useState(false)
    const [showThinking, setShowThinking] = useState(false)
    const dispatch = useAppDispatch()

    const {
        sessionId,
        setSessionId,
        sessionData,
        sessionError,
        messages,
        agentStatus,
        isHistoryLoading,
        hydrateSessionHistory,
        resetConversationState,
        sendMessage,
        isSubmitting
    } = useChat()

    const isLoading = useAppSelector(selectIsLoading)
    const lastAssistantMessageRef = useRef<string>('')

    // Set question mode to CHAT when the chat page loads
    useEffect(() => {
        dispatch(setQuestionMode(QUESTION_MODE.CHAT))
    }, [dispatch])

    // Track when agent starts running and show thinking message
    useEffect(() => {
        if (agentStatus === 'running') {
            setShowThinking(true)
        } else {
            setShowThinking(false)
        }
    }, [agentStatus])

    // Hide thinking message when first chunk arrives
    useEffect(() => {
        const lastAssistantMessage = messages
            .filter((m) => m.role === 'assistant')
            .pop()

        if (lastAssistantMessage && agentStatus === 'running') {
            const currentContent = lastAssistantMessage.content || ''
            // If content has changed from empty, we got the first chunk
            if (
                currentContent.length > 0 &&
                currentContent !== lastAssistantMessageRef.current
            ) {
                setShowThinking(false)
            }
            lastAssistantMessageRef.current = currentContent
        }
    }, [messages, agentStatus])

    useEffect(() => {
        setSessionId(initialSessionId)
    }, [initialSessionId, setSessionId])

    useEffect(() => {
        if (!sessionId) {
            resetConversationState()
            return
        }

        // Skip hydration if agent is already running (e.g., navigated from home page with active query)
        if (agentStatus === 'running') {
            return
        }

        hydrateSessionHistory(sessionId).catch((error) => {
            console.error('Failed to hydrate history', error)
        })
    }, [agentStatus, hydrateSessionHistory, resetConversationState, sessionId])

    const handleSend = useCallback(
        async (overrideQuestion?: string) => {
            await sendMessage(overrideQuestion)
        },
        [sendMessage]
    )

    useEffect(() => {
        const rawState =
            (location.state as Record<string, unknown> | null) ?? null
        const pendingQuestion =
            typeof rawState?.pendingQuestion === 'string'
                ? (rawState.pendingQuestion as string)
                : undefined

        if (pendingQuestion) {
            handleSend(pendingQuestion)
            if (rawState) {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { pendingQuestion: _ignored, ...rest } = rawState
                const nextState = Object.keys(rest).length > 0 ? rest : null
                navigateTo('.', { replace: true, state: nextState })
            } else {
                navigateTo('.', { replace: true, state: null })
            }
        }
    }, [handleSend, location.state, navigateTo])

    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault()
                if (!isSubmitting && agentStatus !== 'running') {
                    handleSend()
                }
            }
        },
        [agentStatus, handleSend, isSubmitting]
    )

    // Only update URL when a new session is created (not when loading existing session)
    // This prevents circular updates while allowing new session navigation
    useEffect(() => {
        // If we have a sessionId in state but no ID in URL, it means a new session was created
        if (sessionId && !initialSessionId) {
            setSearchParams({ id: sessionId })
        }
    }, [sessionId, initialSessionId, setSearchParams])

    return (
        <div className="flex h-screen">
            <SidebarProvider>
                <div className="flex-1">
                    <AgentHeader sessionData={sessionData} />
                    <Sidebar className="block md:hidden" />
                    <div className="flex justify-center h-[calc(100vh-53px)]">
                        <div className="flex-1 flex flex-col max-w-4xl p-3 md:p-4">
                            <Conversation className="flex-1">
                                <ConversationContent className="p-0 md:p-2">
                                    {isHistoryLoading && (
                                        <div className="text-sm text-neutral-500">
                                            Loading conversation history&hellip;
                                        </div>
                                    )}
                                    {sessionError && (
                                        <div className="mb-4 rounded border border-red-500/40 bg-red-500/10 p-3 text-sm text-red-500 dark:text-red-300">
                                            {sessionError}
                                        </div>
                                    )}
                                    {!isHistoryLoading &&
                                        !sessionError &&
                                        messages.length === 0 && (
                                            <div className="text-sm text-neutral-500 text-center py-12">
                                                Ask anything&mdash;your
                                                assistant is ready to help.
                                            </div>
                                        )}

                                    {messages.map((message) => (
                                        <ChatMessageContent
                                            key={message.id}
                                            message={message}
                                        />
                                    ))}

                                    {showThinking && <ThinkingMessage />}
                                </ConversationContent>
                                <ConversationScrollButton />
                            </Conversation>

                            <div className="flex flex-col items-start gap-2">
                                <div className="flex gap-x-2">
                                    <ModelTag />
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <ButtonIcon
                                                name="setting"
                                                onClick={() =>
                                                    setIsOpenSetting(true)
                                                }
                                            />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            Settings
                                        </TooltipContent>
                                    </Tooltip>
                                </div>
                                <QuestionInput
                                    hideSuggestions
                                    className="w-full max-w-none"
                                    textareaClassName="min-h-30 h-30 w-full"
                                    placeholder="Ask me anything..."
                                    value=""
                                    handleKeyDown={handleKeyDown}
                                    handleSubmit={handleSend}
                                    hideFeatureSelector
                                    isDisabled={isLoading}
                                    hideModeSelector
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </SidebarProvider>
            <RightSidebar />
            <AgentSetting
                isOpen={isOpenSetting}
                onOpenChange={setIsOpenSetting}
            />
        </div>
    )
}

export const Component = ChatPage
