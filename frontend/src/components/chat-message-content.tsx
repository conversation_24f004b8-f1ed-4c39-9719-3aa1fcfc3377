import { useMemo } from 'react'

import { Response } from '@/components/ai-elements/response'
import { ChatMessage } from '@/utils/chat-events'
import { getFileIconAndColor } from '@/utils/file-utils'
import { MessageContent } from './ai-elements/message'

interface ChatMessageContentProps {
    message: ChatMessage
}

const ChatMessageContent = ({ message }: ChatMessageContentProps) => {
    const fileElements = useMemo(() => {
        if (!message.files || message.files.length === 0) return null

        const filesToDisplay = message.files

        return filesToDisplay.map((fileName, fileIndex) => {
            const isImage =
                fileName.match(/\.(jpeg|jpg|gif|png|webp|svg|heic|bmp)$/i) !==
                null

            if (
                isImage &&
                message.fileContents &&
                message.fileContents[fileName]
            ) {
                return (
                    <div
                        key={`${message.id}-file-${fileIndex}`}
                        className="inline-block ml-auto rounded-3xl overflow-hidden max-w-[320px]"
                    >
                        <div className="w-40 h-40 rounded-xl overflow-hidden">
                            <img
                                src={message.fileContents[fileName]}
                                alt={fileName}
                                className="w-full h-full object-cover"
                                loading="eager"
                            />
                        </div>
                    </div>
                )
            }

            const { IconComponent, bgColor, label } =
                getFileIconAndColor(fileName)

            return (
                <div
                    key={`${message.id}-file-${fileIndex}`}
                    className="inline-block ml-auto bg-[#35363a] text-white rounded-2xl px-4 py-3 border border-gray-700 shadow-sm"
                >
                    <div className="flex items-center gap-3">
                        <div
                            className={`flex items-center justify-center w-12 h-12 ${bgColor} rounded-xl`}
                        >
                            <IconComponent className="size-6 text-white" />
                        </div>
                        <div className="flex flex-col">
                            <span className="text-base font-medium">
                                {fileName}
                            </span>
                            <span className="text-left text-sm text-gray-500">
                                {label}
                            </span>
                        </div>
                    </div>
                </div>
            )
        })
    }, [message.files, message.fileContents, message.id])

    return (
        <div
            className={`rounded-lg py-4 text-sm ${
                message.role === 'user'
                    ? 'flex flex-col items-end justify-end gap-2'
                    : message.role === 'system'
                      ? 'p-3 border italic w-full text-gray-500 dark:text-gray-400'
                      : 'text-white w-full'
            }`}
        >
            {fileElements && (
                <div className="flex flex-wrap gap-2 justify-end max-w-full">
                    {fileElements.length === 1 ? (
                        fileElements
                    ) : (
                        <div className="grid grid-cols-2 gap-2 w-full">
                            {fileElements}
                        </div>
                    )}
                </div>
            )}
            {message.role === 'user' ? (
                <div className="w-fit bg-[#f5f5f5] dark:bg-grey rounded-lg p-3 max-w-[80%] text-black whitespace-pre-wrap border border-grey dark:none">
                    {message.content}
                </div>
            ) : (
                <MessageContent variant="flat" className="p-0">
                    <Response>{message.content}</Response>
                </MessageContent>
            )}
        </div>
    )
}

export default ChatMessageContent
