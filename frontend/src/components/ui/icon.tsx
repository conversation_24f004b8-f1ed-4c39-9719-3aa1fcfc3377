import SVG from 'react-inlinesvg'

interface IconProps {
    name: string
    className?: string
    color?: string
    style?: React.CSSProperties
}

export function Icon({ name, className = 'size-6', ...props }: IconProps) {
    if (name === 'claude')
        return (
            <img src={`/images/claude.png`} className={className} {...props} />
        )
    return <SVG src={`/icons/${name}.svg`} className={className} {...props} />
}
