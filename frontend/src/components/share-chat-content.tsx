import { useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router'

import <PERSON><PERSON><PERSON>er from '@/components/header'
import RightSidebar from '@/components/right-sidebar'
import ChatMessageContent from '@/components/chat-message-content'
import Sidebar from '@/components/sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'
import { sessionService } from '@/services/session.service'
import { chatService } from '@/services/chat.service'
import { ISession } from '@/typings/agent'
import { buildMessagesFromChatHistory, type ChatMessage } from '@/utils/chat-events'

export function ShareChatContent() {
    const { sessionId } = useParams()
    const messagesEndRef = useRef<HTMLDivElement | null>(null)
    const [sessionData, setSessionData] = useState<ISession | undefined>(undefined)
    const [messages, setMessages] = useState<ChatMessage[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchSessionData = async () => {
            if (!sessionId) {
                setError('No session ID provided')
                setIsLoading(false)
                return
            }

            try {
                setIsLoading(true)
                const session = await sessionService.getPublicSession(sessionId)
                setSessionData(session)

                // Fetch chat history
                const historyResponse = await chatService.getChatHistory(sessionId)
                const hydratedHistory = buildMessagesFromChatHistory(historyResponse.messages)
                setMessages(hydratedHistory.messages)
                setError(null)
            } catch (err) {
                console.error('Error fetching session data:', err)
                setError('Failed to load conversation')
            } finally {
                setIsLoading(false)
            }
        }

        fetchSessionData()
    }, [sessionId])

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    }, [messages])

    return (
        <div className="flex h-screen">
            <SidebarProvider>
                <div className="flex-1">
                    <AgentHeader sessionData={sessionData} />
                    <Sidebar className="block md:hidden" />
                    <div className="flex justify-center h-[calc(100vh-53px)]">
                        <div className="flex-1 flex flex-col max-w-4xl p-3 md:p-4">
                            <div className="flex-1 overflow-y-auto">
                                {isLoading && (
                                    <div className="text-sm text-neutral-500">
                                        Loading conversation history&hellip;
                                    </div>
                                )}
                                {error && (
                                    <div className="mb-4 rounded border border-red-500/40 bg-red-500/10 p-3 text-sm text-red-500 dark:text-red-300">
                                        {error}
                                    </div>
                                )}
                                {!isLoading && !error && messages.length === 0 && (
                                    <div className="text-sm text-neutral-500 text-center py-12">
                                        No messages in this conversation.
                                    </div>
                                )}

                                {messages.map((message) => (
                                    <ChatMessageContent
                                        key={message.id}
                                        message={message}
                                    />
                                ))}

                                <div ref={messagesEndRef} />
                            </div>
                        </div>
                    </div>
                </div>
            </SidebarProvider>
            <RightSidebar />
        </div>
    )
}
