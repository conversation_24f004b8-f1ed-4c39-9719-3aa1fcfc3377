import {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
    type ReactNode
} from 'react'
import { toast } from 'sonner'

import {
    chatService,
    type ChatQueryPayload,
    type ChatStreamEvent
} from '@/services/chat.service'
import {
    useAppDispatch,
    useAppSelector,
    setLoading,
    setIsCreatingSession,
    setIsFromNewQuestion,
    setCurrentQuestion,
    setRequireClearFiles,
    resetSlideTemplate,
    setActiveSessionId,
    selectSelectedModel,
    selectAvailableModels,
    selectSelectedSlideTemplate,
    clearCurrentMessageFileIds,
    selectCurrentMessageFileIds,
    selectUploadedFiles
} from '@/state'
import { isImageFile } from '@/lib/utils'
import { type ISession } from '@/typings/agent'
import { sessionService } from '@/services/session.service'
import {
    buildMessagesFromChatHistory,
    type AgentStatusState,
    type ChatMessage
} from '@/utils/chat-events'

interface UseChatQueryOptions {
    autoStopOnUnmount?: boolean
}

type StreamCallbacks = {
    onSession?: (params: { sessionId: string; isNewSession: boolean }) => void
    onToken?: (token: string) => void
    onDone?: () => void
    onError?: (message?: string) => void
}

type SubmitOptions =
    | string
    | {
          sessionId?: string
          callbacks?: StreamCallbacks
      }

type SubmitOptionsExtracted = {
    sessionId?: string
    callbacks?: StreamCallbacks
}

function extractSubmitOptions(value?: SubmitOptions): SubmitOptionsExtracted {
    if (!value) return { sessionId: undefined, callbacks: undefined }
    if (typeof value === 'string') {
        return { sessionId: value, callbacks: undefined }
    }
    return {
        sessionId: value.sessionId,
        callbacks: value.callbacks
    }
}

type UploadedFile = {
    id: string
    name: string
    path: string
    size: number
}

function useChatTransport(options?: UseChatQueryOptions) {
    const autoStopOnUnmount = options?.autoStopOnUnmount ?? true
    const dispatch = useAppDispatch()
    const selectedModelId = useAppSelector(selectSelectedModel)
    const availableModels = useAppSelector(selectAvailableModels)
    const selectedSlideTemplate = useAppSelector(selectSelectedSlideTemplate)
    const currentMessageFileIds = useAppSelector(selectCurrentMessageFileIds)

    const [isSubmitting, setIsSubmitting] = useState(false)
    const activeStreamControllerRef = useRef<AbortController | null>(null)

    const stopActiveStream = useCallback(() => {
        if (activeStreamControllerRef.current) {
            activeStreamControllerRef.current.abort()
            activeStreamControllerRef.current = null
        }
    }, [])

    const submitChatQuery = useCallback(
        async (question: string, options?: SubmitOptions): Promise<void> => {
            const trimmedQuestion = question.trim()
            if (!trimmedQuestion) {
                toast.error('Please enter a question before submitting.')
                return undefined
            }

            stopActiveStream()

            setIsSubmitting(true)
            dispatch(setLoading(true))

            const { sessionId, callbacks } = extractSubmitOptions(options)

            if (!sessionId) {
                dispatch(setIsCreatingSession(true))
            }

            try {
                const model =
                    availableModels.find(
                        (item) => item.id === selectedModelId
                    ) ?? availableModels[0]

                if (!model) {
                    toast.error(
                        'No AI model is configured. Please add a model in settings first.'
                    )
                    throw new Error('No model available')
                }

                const payload: ChatQueryPayload = {
                    session_id: sessionId,
                    model_id: model.id,
                    text: trimmedQuestion,
                    files: currentMessageFileIds
                }

                dispatch(setCurrentQuestion(''))
                dispatch(setRequireClearFiles(true))
                if (selectedSlideTemplate) {
                    dispatch(resetSlideTemplate())
                }

                const controller = new AbortController()
                activeStreamControllerRef.current = controller
                let sessionEstablished = Boolean(sessionId)

                await chatService.streamQuery(payload, {
                    signal: controller.signal,
                    onEvent: (event: ChatStreamEvent) => {
                        switch (event.type) {
                            case 'session': {
                                const isNewSession =
                                    event.is_new_session ?? !sessionEstablished
                                sessionEstablished = true
                                dispatch(setActiveSessionId(event.session_id))
                                dispatch(setIsCreatingSession(false))
                                if (isNewSession) {
                                    dispatch(setIsFromNewQuestion(true))
                                }
                                callbacks?.onSession?.({
                                    sessionId: event.session_id,
                                    isNewSession
                                })
                                break
                            }
                            case 'token': {
                                callbacks?.onToken?.(event.content)
                                break
                            }
                            case 'done': {
                                activeStreamControllerRef.current = null
                                callbacks?.onDone?.()
                                break
                            }
                            case 'error': {
                                activeStreamControllerRef.current = null
                                callbacks?.onError?.(event.message)
                                break
                            }
                            default:
                                break
                        }
                    }
                })
                dispatch(clearCurrentMessageFileIds())
            } catch (error) {
                console.error('Failed to submit chat query', error)
                callbacks?.onError?.(
                    error instanceof Error ? error.message : undefined
                )
                toast.error(
                    'Unable to submit your question right now. Please try again.'
                )
                throw error
            } finally {
                stopActiveStream()
                dispatch(setIsCreatingSession(false))
                dispatch(setLoading(false))
                setIsSubmitting(false)
            }
        },
        [
            availableModels,
            currentMessageFileIds,
            clearCurrentMessageFileIds,
            dispatch,
            selectedModelId,
            selectedSlideTemplate,
            stopActiveStream
        ]
    )

    useEffect(() => {
        if (!autoStopOnUnmount) {
            return undefined
        }

        return () => {
            stopActiveStream()
        }
    }, [autoStopOnUnmount, stopActiveStream])

    return { submitChatQuery, isSubmitting, stopActiveStream }
}

type ChatSharedState = {
    sessionId: string | null
    sessionData?: ISession
    sessionError: string | null
    messages: ChatMessage[]
    agentStatus: AgentStatusState
    inputValue: string
    isHistoryLoading: boolean
}

type ChatContextValue = ChatSharedState & {
    isSubmitting: boolean
    sendMessage: (overrideQuestion?: string) => Promise<void>
    stopActiveStream: () => void
    resetConversationState: () => void
    hydrateSessionHistory: (sessionId: string) => Promise<void>
    setInputValue: (value: string) => void
    setSessionId: (sessionId: string | null) => void
}

const INITIAL_CHAT_STATE: ChatSharedState = {
    sessionId: null,
    sessionData: undefined,
    sessionError: null,
    messages: [],
    agentStatus: 'ready',
    inputValue: '',
    isHistoryLoading: false
}

const ChatContext = createContext<ChatContextValue | undefined>(undefined)

function useChatProviderValue(): ChatContextValue {
    const { submitChatQuery, isSubmitting, stopActiveStream } =
        useChatTransport({ autoStopOnUnmount: false })
    const currentMessageFileIds = useAppSelector(selectCurrentMessageFileIds)
    const uploadedFiles = useAppSelector(selectUploadedFiles) as UploadedFile[]

    const [state, setState] = useState<ChatSharedState>(INITIAL_CHAT_STATE)
    const stateRef = useRef(state)

    const streamingMessageIdRef = useRef<string | null>(null)
    const activeSessionIdRef = useRef<string | null>(null)

    const setChatState = useCallback(
        (
            updater:
                | Partial<ChatSharedState>
                | ((prev: ChatSharedState) => ChatSharedState)
        ) => {
            setState((prev) => {
                const next =
                    typeof updater === 'function'
                        ? (
                              updater as (
                                  prevState: ChatSharedState
                              ) => ChatSharedState
                          )(prev)
                        : { ...prev, ...updater }
                stateRef.current = next
                return next
            })
        },
        []
    )

    useEffect(() => {
        stateRef.current = state
    }, [state])

    const resetConversationState = useCallback(() => {
        streamingMessageIdRef.current = null
        setChatState((prev) => ({
            ...prev,
            messages: [],
            agentStatus: 'ready'
        }))
    }, [setChatState])

    const hydrateSessionHistory = useCallback(
        async (activeSessionId: string) => {
            setChatState((prev) => ({
                ...prev,
                isHistoryLoading: true
            }))

            try {
                const [session, chatHistory] = await Promise.all([
                    sessionService.getSession(activeSessionId),
                    chatService.getChatHistory(activeSessionId)
                ])

                const hydrated = buildMessagesFromChatHistory(
                    chatHistory.messages ?? []
                )

                streamingMessageIdRef.current = null
                activeSessionIdRef.current = activeSessionId

                // Only update state, don't update sessionId to prevent circular updates
                // sessionId should only be set via setSessionId from URL changes
                setChatState((prev) => ({
                    ...prev,
                    // Don't update sessionId here - it's already set from the URL
                    sessionData: session,
                    sessionError: null,
                    messages: hydrated.messages,
                    agentStatus: 'ready'
                }))
            } catch (error) {
                console.error('Failed to load session history', error)
                // setChatState((prev) => ({
                //     ...prev,
                //     sessionData: undefined,
                //     sessionError:
                //         'We could not load this chat session. It may have been deleted or you might not have access.',
                //     messages: [],
                //     agentStatus: 'ready'
                // }))
                // resetConversationState()
            } finally {
                setChatState((prev) => ({
                    ...prev,
                    isHistoryLoading: false
                }))
            }
        },
        [resetConversationState, setChatState]
    )

    const setSessionId = useCallback(
        (sessionId: string | null) => {
            // If switching to a different session, clear messages immediately
            const isDifferentSession = sessionId !== activeSessionIdRef.current

            activeSessionIdRef.current = sessionId
            setChatState((prev) => ({
                ...prev,
                sessionId,
                ...(sessionId === null
                    ? {
                          sessionData: undefined,
                          sessionError: null,
                          messages: [],
                          agentStatus: 'ready' as const
                      }
                    : isDifferentSession
                      ? {
                          // Clear messages when switching to a different session
                          messages: [],
                          sessionData: undefined,
                          sessionError: null,
                          isHistoryLoading: false
                      }
                      : {})
            }))
        },
        [setChatState]
    )

    const setInputValue = useCallback(
        (value: string) => {
            setChatState((prev) => ({
                ...prev,
                inputValue: value
            }))
        },
        [setChatState]
    )

    const sendMessage = useCallback(
        async (overrideQuestion?: string) => {
            const rawQuestion =
                typeof overrideQuestion === 'string'
                    ? overrideQuestion
                    : stateRef.current.inputValue
            const trimmed = rawQuestion.trim()
            if (!trimmed) return

            const createdAt = new Date().toISOString()
            const timestamp = Date.now()
            const userMessageId = `user-${timestamp}`
            const assistantMessageId = `assistant-${timestamp}`

            streamingMessageIdRef.current = assistantMessageId
            activeSessionIdRef.current = stateRef.current.sessionId

            const attachments = currentMessageFileIds
                .map((fileId) =>
                    uploadedFiles.find((file) => file.id === fileId)
                )
                .filter((file): file is UploadedFile => Boolean(file))

            const userMessageFiles =
                attachments.length > 0
                    ? attachments.map((file) => file.name)
                    : undefined

            const userMessageFileContents =
                attachments.reduce<Record<string, string>>((acc, file) => {
                    if (isImageFile(file.name)) {
                        acc[file.name] = file.path
                    }
                    return acc
                }, {})

            const userMessage: ChatMessage = {
                id: userMessageId,
                role: 'user',
                content: trimmed,
                createdAt,
                ...(userMessageFiles ? { files: userMessageFiles } : {}),
                ...(Object.keys(userMessageFileContents).length
                    ? { fileContents: userMessageFileContents }
                    : {})
            }

            setChatState((prev) => {
                const base = prev.sessionId ? [...prev.messages] : []
                return {
                    ...prev,
                    inputValue: '',
                    messages: [
                        ...base,
                        userMessage,
                        {
                            id: assistantMessageId,
                            role: 'assistant',
                            content: '',
                            createdAt
                        }
                    ],
                    agentStatus: 'running',
                    sessionError: null
                }
            })

            try {
                await submitChatQuery(trimmed, {
                    sessionId: stateRef.current.sessionId ?? undefined,
                    callbacks: {
                        onSession: ({ sessionId: newSessionId }) => {
                            if (!newSessionId) return
                            activeSessionIdRef.current = newSessionId
                            setChatState((prev) => ({
                                ...prev,
                                sessionId: newSessionId
                            }))
                        },
                        onToken: (token) => {
                            const targetId = streamingMessageIdRef.current
                            if (!targetId) return
                            setChatState((prev) => ({
                                ...prev,
                                messages: prev.messages.map((message) =>
                                    message.id === targetId
                                        ? {
                                              ...message,
                                              content: `${message.content ?? ''}${token}`
                                          }
                                        : message
                                )
                            }))
                        },
                        onDone: () => {
                            streamingMessageIdRef.current = null
                            setChatState((prev) => ({
                                ...prev,
                                agentStatus: 'ready'
                            }))
                            // Use the current state's sessionId instead of the ref to avoid stale values
                            const targetSessionId = stateRef.current.sessionId
                            if (targetSessionId) {
                                void hydrateSessionHistory(targetSessionId)
                            }
                        },
                        onError: () => {
                            streamingMessageIdRef.current = null
                            setChatState((prev) => ({
                                ...prev,
                                agentStatus: 'ready'
                            }))
                        }
                    }
                })
            } catch {
                streamingMessageIdRef.current = null
                setChatState((prev) => ({
                    ...prev,
                    agentStatus: 'ready'
                }))
            }
        },
        [
            currentMessageFileIds,
            hydrateSessionHistory,
            setChatState,
            submitChatQuery,
            uploadedFiles
        ]
    )

    useEffect(() => {
        return () => {
            stopActiveStream()
        }
    }, [stopActiveStream])

    const value = useMemo<ChatContextValue>(
        () => ({
            ...state,
            isSubmitting,
            sendMessage,
            stopActiveStream,
            resetConversationState,
            hydrateSessionHistory,
            setInputValue,
            setSessionId
        }),
        [
            hydrateSessionHistory,
            isSubmitting,
            resetConversationState,
            sendMessage,
            setInputValue,
            setSessionId,
            state,
            stopActiveStream
        ]
    )

    return value
}

export function ChatProvider({ children }: { children: ReactNode }) {
    const value = useChatProviderValue()
    return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}

export function useChat() {
    const context = useContext(ChatContext)
    if (!context) {
        throw new Error('useChat must be used within a ChatProvider')
    }
    return context
}

export function useChatQuery() {
    return useChat()
}
