export interface ChatQueryPayload {
    session_id?: string
    model_id: string
    text: string
    files: string[]
}

export type ChatStreamEvent =
    | {
          type: 'session'
          session_id: string
          is_new_session?: boolean
          name?: string
          agent_type?: string
          model_id?: string
          created_at?: string
      }
    | {
          type: 'content_start'
      }
    | {
          type: 'token'
          content: string
      }
    | {
          type: 'complete'
          message_id?: string
          finish_reason?: string
          elapsed_ms?: number
      }
    | {
          type: 'done'
      }
    | {
          type: 'error'
          message?: string
      }

export interface ChatStreamOptions {
    signal?: AbortSignal
    onEvent: (event: ChatStreamEvent) => void
}

export interface MessagePart {
    type: string
    data: {
        text?: string
        [key: string]: unknown
    }
}

export interface ChatMessage {
    parts_json: string // JSON string containing MessagePart[]
}

export interface ChatHistoryMessage {
    id: string
    role: 'user' | 'assistant'
    message: ChatMessage
    usage: {
        completion_tokens: number
        prompt_tokens: number
        total_tokens: number
        completion_tokens_details: {
            reasoning_tokens: number
        } | null
        prompt_tokens_details: unknown | null
    } | null
    tokens: number | null
    model: string | null
    created_at: string
}

export interface ChatHistoryResponse {
    messages: ChatHistoryMessage[]
    has_more: boolean
    total_count: number
}

/**
 * Extracts text content from a message's parts_json field
 * @param message - The chat message containing parts_json
 * @returns The concatenated text content from all text parts
 */
export function extractTextFromMessage(message: ChatMessage): string {
    try {
        const parts = JSON.parse(message.parts_json) as MessagePart[]
        return parts
            .filter(part => part.type === 'text' && part.data.text)
            .map(part => part.data.text)
            .join('')
    } catch (error) {
        console.error('Failed to parse parts_json:', error)
        return ''
    }
}
