export type AgentStatusState = 'ready' | 'running'

export type ChatMessage = {
    id: string
    role: 'user' | 'assistant' | 'system'
    content: string
    createdAt?: string
    isError?: boolean
    files?: string[]
    fileContents?: Record<string, string>
}

type HydratedHistory = {
    messages: ChatMessage[]
    processedIds: Set<string>
    lastCreatedAt: string | null
}

const DEFAULT_HISTORY: HydratedHistory = {
    messages: [],
    processedIds: new Set<string>(),
    lastCreatedAt: null
}

const ROLE_VALUES = new Set(['user', 'assistant', 'system'])

type MessagePart = {
    type: string
    data: {
        text?: string
        [key: string]: unknown
    }
}

const normalizeContent = (message: unknown): string => {
    // If message is an object with parts_json field
    if (message && typeof message === 'object' && 'parts_json' in message) {
        const partsJson = (message as { parts_json?: unknown }).parts_json
        if (typeof partsJson === 'string') {
            try {
                const parts = JSON.parse(partsJson) as MessagePart[]
                return parts
                    .filter(part => part.type === 'text' && part.data.text)
                    .map(part => part.data.text)
                    .join('')
            } catch (error) {
                console.error('Failed to parse parts_json:', error)
                return ''
            }
        }
    }

    // Legacy format handling
    if (typeof message === 'string') {
        return message
    }

    if (Array.isArray(message)) {
        return message
            .map((item) => {
                if (typeof item === 'string') return item
                if (item && typeof item === 'object') {
                    const maybeText = (item as { text?: unknown }).text
                    if (typeof maybeText === 'string') return maybeText
                }
                return ''
            })
            .join('')
    }

    if (message && typeof message === 'object') {
        const maybeText = (message as { text?: unknown }).text
        if (typeof maybeText === 'string') {
            return maybeText
        }
    }

    return ''
}

const normalizeRole = (
    value: unknown,
    fallback: 'assistant' | 'user' | 'system' = 'assistant'
): 'assistant' | 'user' | 'system' => {
    if (typeof value === 'string' && ROLE_VALUES.has(value)) {
        return value as 'assistant' | 'user' | 'system'
    }
    return fallback
}

const normalizeCreatedAt = (value: unknown): string | undefined => {
    if (typeof value === 'string') return value
    if (typeof value === 'number') {
        return new Date(value * 1000).toISOString()
    }
    return undefined
}

export function buildMessagesFromChatHistory(
    historyMessages: Array<{
        id: string
        role: 'user' | 'assistant'
        message: {
            parts_json?: string
            content?: string
            role?: 'user' | 'assistant'
        }
        created_at: string
    }>
): HydratedHistory {
    if (!Array.isArray(historyMessages)) {
        return {
            ...DEFAULT_HISTORY,
            processedIds: new Set<string>()
        }
    }

    const processedIds = new Set<string>()
    const messages: ChatMessage[] = []
    let lastCreatedAt: string | null = null

    historyMessages.forEach((historyMsg) => {
        if (!historyMsg || typeof historyMsg !== 'object') return

        const id = historyMsg.id

        if (processedIds.has(id)) return
        processedIds.add(id)

        const role = normalizeRole(historyMsg.role ?? historyMsg.message?.role)
        const content = normalizeContent(historyMsg.message)
        const createdAt = normalizeCreatedAt(historyMsg.created_at)

        messages.push({
            id,
            role,
            content,
            createdAt,
            isError: false
        })

        if (createdAt) {
            lastCreatedAt = createdAt
        }
    })

    return {
        messages,
        processedIds,
        lastCreatedAt
    }
}
