# This is a config for E2B sandbox template.
# You can use template ID (01btuya8bgbs801maahh) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox.create("01btuya8bgbs801maahh") # Sync sandbox
# sandbox = await AsyncSandbox.create("01btuya8bgbs801maahh") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('01btuya8bgbs801maahh')

memory_mb = 4_096
cpu_count = 4
start_cmd = "/app/start-services.sh"
dockerfile = "e2b.Dockerfile"
template_id = "01btuya8bgbs801maahh"
