export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/Documents/ii_agent_vertex_ai_service_account.json"

# Web Search
export WEB_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"

# Image Search
export IMAGE_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"

# Web Visit
export WEB_VISIT_FIRECRAWL_API_KEY="fc-9e8a7d0491914b28830b555942769bb4"
export WEB_VISIT_GEMINI_API_KEY="AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA"

# Video Generate
export VIDEO_GENERATE_GCP_PROJECT_ID="backend-alpha-97077"
export VIDEO_GENERATE_GCP_LOCATION="us-central1"
export VIDEO_GENERATE_GCS_OUTPUT_BUCKET="ii-agent-dev"

# Image Generate
export IMAGE_GENERATE_GCP_PROJECT_ID="backend-alpha-97077"
export IMAGE_GENERATE_GCP_LOCATION="us-central1"
export IMAGE_GENERATE_GCS_OUTPUT_BUCKET="ii-agent-dev"

# Database
export DATABASE_NEON_DB_API_KEY="napi_8120jmq0t7llvnfj3h9uim3etqdmw9jg8j8vf97g7sdgt41qci170optgkicqfye"

# Enhance Prompt
export ENHANCE_PROMPT_OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Compressor
export COMPRESSOR_COMPRESS_TYPE='["llm"]'
export COMPRESSOR_LLM_CONFIG='{
  "model": "gemini-lite",
  "base_url": "http://localhost:4000/",
  "api_key": "dummy"
}'


# Storage Provider
export STORAGE_PROVIDER="gcs"
export GCS_BUCKET_NAME="ii-agent-public"
export GCS_PROJECT_ID="backend-alpha-97077"

export OPENAI_API_KEY="********************************************************************************************************************************************************************"

export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/iiagent"
# remember to install ffmpeg: sudo apt install -y ffmpeg

export RESEARCHER_AGENT_CONFIG='{
  "researcher": {
    "model": "deepseek-reasoner",
    "api_key": "***********************************",
    "base_url" : "https://api.deepseek.com/beta",
    "api_type": "openai"
  },
  "final_report_builder": {
    "model": "gemini-2.5-pro",
    "api_key": "AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA",
    "base_url": null,
    "max_retries": 10,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "gemini",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false

  },
  "report_builder": {
    "model": "gemini-2.5-flash",
    "api_key": "AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA",
    "base_url": null,
    "max_retries": 10,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "gemini",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  }
}'

export LLM_CONFIGS='{"gemini/gemini-2.5-pro":{"model":"gemini-2.5-pro","api_key":"AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA","base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"vertex/claude-sonnet-4@20250514":{"model":"claude-sonnet-4@20250514","api_key":null,"base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":"us-east5","vertex_project_id":"backend-alpha-97077","api_type":"anthropic","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"default":{"model":"gemini-2.5-pro","api_key":"AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA","base_url":null,"max_retries":10,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false}}'

export TOOL_SERVER_URL="https://587f20befb96.ngrok-free.app"

export IMAGE_EDIT_GCP_PROJECT_ID="backend-alpha-97077"
export IMAGE_EDIT_GCP_LOCATION="us-central1"
export IMAGE_EDIT_GCS_OUTPUT_BUCKET="ii-agent-dev"
export IMAGE_EDIT_MODEL_NAME="gemini-2.5-flash-image"

python -m src.ii_tool.integrations.app.main --port 1235
