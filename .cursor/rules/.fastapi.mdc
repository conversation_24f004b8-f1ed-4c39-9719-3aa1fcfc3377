---
description: 
globs: **/*.py,src/**/*.py,src/backend/vireox/**/*.py
alwaysApply: false
---
---
description: FastAPI best practices and patterns for building modern Python web APIs
globs: **/*.py, src/**/*.py, src/**/*.py
---

# FastAPI Best Practices

## Project Structure
- Use proper directory structure
- Implement proper module organization
- Use proper dependency injection
- Keep routes organized by domain
- Implement proper middleware
- Use proper configuration management

## API Design
- Use proper HTTP methods
- Implement proper status codes
- Use proper request/response models
- Implement proper validation
- Use proper error handling
- Document APIs with OpenAPI

## Models
- Use Pydantic models
- Implement proper validation
- Use proper type hints
- Keep models organized
- Use proper inheritance
- Implement proper serialization

## Database
- Use proper ORM (SQLAlchemy)
- Implement proper migrations
- Use proper connection pooling
- Implement proper transactions
- Use proper query optimization
- Handle database errors properly

## Authentication
- Implement proper JWT authentication
- Use proper password hashing
- Implement proper role-based access
- Use proper session management
- Implement proper OAuth2
- Handle authentication errors properly

## Security
- Implement proper CORS
- Use proper rate limiting
- Implement proper input validation
- Use proper security headers
- Handle security errors properly
- Implement proper logging

## Performance
- Use proper caching
- Implement proper async operations
- Use proper background tasks
- Implement proper connection pooling
- Use proper query optimization
- Monitor performance metrics

## Testing
- Write proper unit tests
- Implement proper integration tests
- Use proper test fixtures
- Implement proper mocking
- Test error scenarios
- Use proper test coverage

## Deployment
- Use proper Docker configuration
- Implement proper CI/CD
- Use proper environment variables
- Implement proper logging
- Use proper monitoring
- Handle deployment errors properly

## Documentation
- Use proper docstrings
- Implement proper API documentation
- Use proper type hints
- Keep documentation updated
- Document error scenarios
- Use proper versioning 



# Database query pattern.
Follow this pattern for database async query. 

```
    @classmethod
    async def find_latest_by_session_status(
        cls, *, db_session: AsyncSession, session_id: UUID, status: TriggeredStatus
    ) -> Optional["SessionTriggeredTasks"]:
        result = await db_session.execute(
            select(SessionTriggeredTasks)
            .where(
                SessionTriggeredTasks.session_id == str(session_id),
                SessionTriggeredTasks.status == status.value,
            )
            .order_by(SessionTriggeredTasks.created_at.desc())
        )
        return result.scalar_one_or_none()
```