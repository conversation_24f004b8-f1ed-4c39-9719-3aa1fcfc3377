---
description: 
globs: 
alwaysApply: true
---
---
description: Guidelines for writing clean, maintainable, and human-readable code. Apply these rules when writing or reviewing code to ensure consistency and quality
globs: 
---
# Code Quality Guidelines

## Verify Information
- Always verify information before presenting it. Do not make assumptions or speculate without clear evidence.

## File-by-File Changes
- Make changes file by file and give me a chance to spot mistakes.

## No Apologies
- Never use apologies.

## No Understanding Feedback
- Avoid giving feedback about understanding in comments or documentation.

## No Whitespace Suggestions
- Don't suggest whitespace changes.

## No Summaries
- Don't summarize changes made.
## No Inventions
- Don't invent changes other than what's explicitly requested.

## No Unnecessary Confirmations
- Don't ask for confirmation of information already provided in the context.

## Preserve Existing Code
- Don't remove unrelated code or functionalities. Pay attention to preserving existing structures.

## Single Chunk Edits
- Provide all edits in a single chunk instead of multiple-step instructions or explanations for the same file.

## No Implementation Checks
- Don't ask the user to verify implementations that are visible in the provided context.

## No Unnecessary Updates
- Don't suggest updates or changes to files when there are no actual modifications needed.

## Provide Real File Links
- Always provide links to the real files, not x.md.

## No Current Implementation
- Don't show or discuss the current implementation unless specifically requested.

# Clean Code Guidelines

## Constants Over Magic Numbers
- Replace hard-coded values with named constants
- Use descriptive constant names that explain the value's purpose
- Keep constants at the top of the file or in a dedicated constants file

## Meaningful Names
- Variables, functions, and classes should reveal their purpose
- Names should explain why something exists and how it's used
- Avoid abbreviations unless they're universally understood

## Smart Comments
- Don't comment on what the code does - make the code self-documenting
- Use comments to explain why something is done a certain way
- Document APIs, complex algorithms, and non-obvious side effects

## Single Responsibility
- Each function should do exactly one thing
- Functions should be small and focused
- If a function needs a comment to explain what it does, it should be split

## DRY (Don't Repeat Yourself)
- Extract repeated code into reusable functions
- Share common logic through proper abstraction
- Maintain single sources of truth

## Clean Structure
- Keep related code together
- Organize code in a logical hierarchy
- Use consistent file and folder naming conventions

## Encapsulation
- Hide implementation details
- Expose clear interfaces
- Move nested conditionals into well-named functions

## Code Quality Maintenance
- Refactor continuously
- Fix technical debt early
- Leave code cleaner than you found it

## Testing
- Write tests before fixing bugs
- Keep tests readable and maintainable
- Test edge cases and error conditions

## Version Control
- Write clear commit messages
- Make small, focused commits
- Use meaningful branch names 
