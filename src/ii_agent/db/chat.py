"""Database models for chat mode."""

from datetime import datetime, timezone
import uuid

from sqlalchemy import ARRAY, BigInteger, Boolean, Column, ForeignKey, Index, String
from sqlalchemy.dialects.postgresql import JSONB, UUID

from ii_agent.db.models import Base, TimestampColumn


class ChatMessage(Base):
    """Chat messages for chat mode conversations.

    Stores messages in OpenAI chat completion format:
    - message: Full message object with content, role, tool_calls, etc.
    - usage: Token usage statistics from LLM response
    - tokens: Accumulated total tokens
    """

    __tablename__ = "chat_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(
        String, nullable=False
    )
    role = Column(String, nullable=False)  # "user" or "assistant"
    content = Column(JSONB, nullable=False)  # Full message object from LLM response
    usage = Column(
        JSONB, nullable=True
    )  # Usage statistics (prompt_tokens, completion_tokens, etc.)
    tokens = Column(BigInteger, nullable=True)  # Total accumulated tokens
    model = Column(String, nullable=True)
    tools = Column(JSONB, nullable=True)  # Tools used in the message
    file_ids = Column(
        ARRAY(UUID), nullable=True
    )  # Array of file IDs associated with the message
    created_at = Column(TimestampColumn, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TimestampColumn,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    parent_message_id = Column(
        UUID(as_uuid=True),
        nullable=True,
    )  # Link to parent message (user message for assistant responses)
    is_finished = Column(
        Boolean, nullable=True, default=True
    )  # Indicates if message is complete

    __table_args__ = (
        Index("idx_chat_messages_session", "session_id"),
        Index("idx_chat_messages_created", "created_at"),
        Index("idx_chat_messages_session_created", "session_id", "created_at"),
    )
