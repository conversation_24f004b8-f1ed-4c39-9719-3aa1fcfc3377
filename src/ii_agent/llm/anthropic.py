import asyncio
import random
import time
from typing import Any, <PERSON><PERSON>, cast
import anthropic
from anthropic import (
    NOT_GIVEN as Anthropic_NOT_GIVEN,
)
from anthropic import (
    APIConnectionError as AnthropicAPIConnectionError,
)
from anthropic import (
    InternalServerError as AnthropicInternalServerError,
)
from anthropic import (
    RateLimitError as AnthropicRateLimitError,
)
from anthropic._exceptions import (
    OverloadedError as AnthropicOverloadedError,  # pyright: ignore[reportPrivateImportUsage]
)
from anthropic.types import (
    TextBlock as AnthropicTextBlock,
    ThinkingBlock as AnthropicThinkingBlock,
    RedactedThinking<PERSON>lock as AnthropicRedactedThinkingBlock,
    ImageBlockParam as AnthropicImageBlockParam,
)
from anthropic.types import ToolParam as AnthropicToolParam
from anthropic.types import (
    ToolResultBlockParam as AnthropicToolResultBlockParam,
)
from anthropic.types import (
    Tool<PERSON>se<PERSON><PERSON> as AnthropicToolUseBlock,
)
from anthropic.types.message_create_params import (
    ToolChoiceToolChoiceAny,
    ToolChoiceToolChoiceAuto,
    ToolChoiceToolChoiceTool,
)


from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.llm.base import (
    LLMClient,
    AssistantContentBlock,
    ToolParam,
    TextPrompt,
    ToolCall,
    TextResult,
    LLMMessages,
    ToolFormattedResult,
    UserContentBlock,
    recursively_remove_invoke_tag,
    ImageBlock,
    ThinkingBlock,
    RedactedThinkingBlock,
)


class AnthropicDirectClient(LLMClient):
    """Use Anthropic models via first party API."""

    def __init__(self, llm_config: LLMConfig):
        """Initialize the Anthropic first party client."""
        super().__init__(llm_config)
        # Disable retries since we are handling retries ourselves.
        if (llm_config.vertex_project_id is not None) and (
            llm_config.vertex_region is not None
        ):
            self.client = anthropic.AnthropicVertex(
                project_id=llm_config.vertex_project_id,
                region=llm_config.vertex_region,
                timeout=60 * 5,
                max_retries=1,
            )
            self.async_client = anthropic.AsyncAnthropicVertex(
                project_id=llm_config.vertex_project_id,
                region=llm_config.vertex_region,
                timeout=60 * 5,
                max_retries=1,
            )
        else:
            self.client = anthropic.Anthropic(
                api_key=llm_config.api_key.get_secret_value()
                if llm_config.api_key
                else None,
                max_retries=1,
                timeout=60 * 5,
            )
            self.async_client = anthropic.AsyncAnthropic(
                api_key=llm_config.api_key.get_secret_value()
                if llm_config.api_key
                else None,
                max_retries=1,
                timeout=60 * 5,
            )
            self.model_name = self.model_name.replace(
                "@", "-"
            )  # Quick fix for Anthropic Vertex API
        self.max_retries = llm_config.max_retries
        if (
            "claude-opus-4" in self.model_name or "claude-sonnet-4" in self.model_name
        ):  # Use Interleaved Thinking for Sonnet 4 and Opus 4
            self.headers = {"anthropic-beta": "interleaved-thinking-2025-05-14"}
        else:
            self.headers = None
        self.thinking_tokens = llm_config.thinking_tokens

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Turn GeneralContentBlock into Anthropic message format
        anthropic_messages = []
        for idx, message_list in enumerate(messages):
            role = (
                "user" if isinstance(message_list[0], UserContentBlock) else "assistant"
            )
            message_content_list = []
            for message in message_list:
                # Check string type to avoid import issues particularly with reloads.
                if str(type(message)) == str(TextPrompt):
                    message = cast(TextPrompt, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(ImageBlock):
                    message = cast(ImageBlock, message)
                    message_content = AnthropicImageBlockParam(
                        type="image",
                        source=message.source,
                    )
                elif str(type(message)) == str(TextResult):
                    message = cast(TextResult, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(ToolCall):
                    message = cast(ToolCall, message)
                    message_content = AnthropicToolUseBlock(
                        type="tool_use",
                        id=message.tool_call_id,
                        name=message.tool_name,
                        input=message.tool_input,
                    )
                elif str(type(message)) == str(ToolFormattedResult):
                    message = cast(ToolFormattedResult, message)
                    message_content = AnthropicToolResultBlockParam(
                        type="tool_result",
                        tool_use_id=message.tool_call_id,
                        content=message.tool_output,
                    )
                elif str(type(message)) == str(RedactedThinkingBlock):
                    message = cast(RedactedThinkingBlock, message)
                    # Convert to Anthropic format for API call
                    message_content = AnthropicRedactedThinkingBlock(
                        type="redacted_thinking", data=message.data
                    )
                elif str(type(message)) == str(ThinkingBlock):
                    message = cast(ThinkingBlock, message)
                    # Convert to Anthropic format for API call
                    message_content = AnthropicThinkingBlock(
                        type="thinking",
                        thinking=message.thinking,
                        signature=message.signature,
                    )
                else:
                    print(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                    raise ValueError(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                message_content_list.append(message_content)

            # Anthropic supports up to 4 cache breakpoints, so we put them on the last 4 messages.
            if idx >= len(messages) - 4:
                if isinstance(message_content_list[-1], dict):
                    message_content_list[-1]["cache_control"] = {"type": "ephemeral"}
                else:
                    message_content_list[-1].cache_control = {"type": "ephemeral"}

            anthropic_messages.append(
                {
                    "role": role,
                    "content": message_content_list,
                }
            )

        # Turn tool_choice into Anthropic tool_choice format
        if tool_choice is None:
            tool_choice_param = Anthropic_NOT_GIVEN
        elif tool_choice["type"] == "any":
            tool_choice_param = ToolChoiceToolChoiceAny(type="any")
        elif tool_choice["type"] == "auto":
            tool_choice_param = ToolChoiceToolChoiceAuto(type="auto")
        elif tool_choice["type"] == "tool":
            tool_choice_param = ToolChoiceToolChoiceTool(
                type="tool", name=tool_choice["name"]
            )
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        if len(tools) == 0:
            tool_params = Anthropic_NOT_GIVEN
        else:
            tool_params = [
                AnthropicToolParam(
                    input_schema=tool.input_schema,
                    name=tool.name,
                    description=tool.description,
                )
                for tool in tools
            ]

        response = None

        if thinking_tokens is None:
            thinking_tokens = self.thinking_tokens
        if thinking_tokens and thinking_tokens > 0:
            extra_body = {
                "thinking": {"type": "enabled", "budget_tokens": thinking_tokens}
            }
            temperature = 1
        else:
            extra_body = None

        for retry in range(self.max_retries):
            try:
                response = self.client.messages.create(  # type: ignore
                    max_tokens=max_tokens,
                    messages=anthropic_messages,
                    model=self.model_name,
                    temperature=temperature,
                    system=system_prompt or Anthropic_NOT_GIVEN,
                    tool_choice=tool_choice_param,  # type: ignore
                    tools=tool_params,
                    extra_headers=self.headers,
                    extra_body=extra_body,
                )
                break
            except Exception as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Anthropic request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying LLM request: {retry + 1}/{self.max_retries}")
                    # Sleep 12-18 seconds with jitter to avoid thundering herd.
                    time.sleep(15 * random.uniform(0.8, 1.2))

        # Convert messages back to internal format
        internal_messages = []
        assert response is not None
        for message in response.content:
            if "</invoke>" in str(message):
                warning_msg = "\n".join(
                    ["!" * 80, "WARNING: Unexpected 'invoke' in message", "!" * 80]
                )
                print(warning_msg)

            if str(type(message)) == str(AnthropicTextBlock):
                message = cast(AnthropicTextBlock, message)
                internal_messages.append(TextResult(text=message.text))
            elif str(type(message)) == str(AnthropicRedactedThinkingBlock):
                # Convert Anthropic response back to internal format
                message = cast(AnthropicRedactedThinkingBlock, message)
                internal_messages.append(RedactedThinkingBlock(data=message.data))
            elif str(type(message)) == str(AnthropicThinkingBlock):
                # Convert Anthropic response back to internal format
                message = cast(AnthropicThinkingBlock, message)
                internal_messages.append(
                    ThinkingBlock(
                        thinking=message.thinking, signature=message.signature
                    )
                )
            elif str(type(message)) == str(AnthropicToolUseBlock):
                message = cast(AnthropicToolUseBlock, message)
                internal_messages.append(
                    ToolCall(
                        tool_call_id=message.id,
                        tool_name=message.name,
                        tool_input=recursively_remove_invoke_tag(message.input),
                    )
                )
            else:
                raise ValueError(f"Unknown message type: {type(message)}")

        cache_creation_input_tokens = getattr(
            response.usage, "cache_creation_input_tokens", 0
        )
        cache_read_input_tokens = getattr(response.usage, "cache_read_input_tokens", 0)

        input_tokens = (
            response.usage.input_tokens
            + cache_creation_input_tokens
            + cache_read_input_tokens
        )
        output_tokens = response.usage.output_tokens
        message_metadata = {
            "raw_response": response,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cache_creation_input_tokens": cache_creation_input_tokens,
            "cache_read_input_tokens": cache_read_input_tokens,
        }

        return internal_messages, message_metadata

    async def agenerate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Turn GeneralContentBlock into Anthropic message format
        anthropic_messages = []
        for idx, message_list in enumerate(messages):
            role = (
                "user" if isinstance(message_list[0], UserContentBlock) else "assistant"
            )
            message_content_list = []
            for message in message_list:
                # Check string type to avoid import issues particularly with reloads.
                if str(type(message)) == str(TextPrompt):
                    message = cast(TextPrompt, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(ImageBlock):
                    message = cast(ImageBlock, message)
                    message_content = AnthropicImageBlockParam(
                        type="image",
                        source=message.source,
                    )
                elif str(type(message)) == str(TextResult):
                    message = cast(TextResult, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(ToolCall):
                    message = cast(ToolCall, message)
                    message_content = AnthropicToolUseBlock(
                        type="tool_use",
                        id=message.tool_call_id,
                        name=message.tool_name,
                        input=message.tool_input,
                    )
                elif str(type(message)) == str(ToolFormattedResult):
                    message = cast(ToolFormattedResult, message)
                    message_content = AnthropicToolResultBlockParam(
                        type="tool_result",
                        tool_use_id=message.tool_call_id,
                        content=message.tool_output,
                    )
                elif str(type(message)) == str(RedactedThinkingBlock):
                    message = cast(RedactedThinkingBlock, message)
                    # Convert to Anthropic format for API call
                    message_content = AnthropicRedactedThinkingBlock(
                        type="redacted_thinking", data=message.data
                    )
                elif str(type(message)) == str(ThinkingBlock):
                    message = cast(ThinkingBlock, message)
                    # Convert to Anthropic format for API call
                    message_content = AnthropicThinkingBlock(
                        type="thinking",
                        thinking=message.thinking,
                        signature=message.signature,
                    )
                else:
                    print(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                    raise ValueError(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                message_content_list.append(message_content)

            # Anthropic supports up to 4 cache breakpoints, so we put them on the last 4 messages.
            if idx >= len(messages) - 4:
                if isinstance(message_content_list[-1], dict):
                    message_content_list[-1]["cache_control"] = {"type": "ephemeral"}
                else:
                    message_content_list[-1].cache_control = {"type": "ephemeral"}

            anthropic_messages.append(
                {
                    "role": role,
                    "content": message_content_list,
                }
            )

        # Turn tool_choice into Anthropic tool_choice format
        if tool_choice is None:
            tool_choice_param = Anthropic_NOT_GIVEN
        elif tool_choice["type"] == "any":
            tool_choice_param = ToolChoiceToolChoiceAny(type="any")
        elif tool_choice["type"] == "auto":
            tool_choice_param = ToolChoiceToolChoiceAuto(type="auto")
        elif tool_choice["type"] == "tool":
            tool_choice_param = ToolChoiceToolChoiceTool(
                type="tool", name=tool_choice["name"]
            )
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        if len(tools) == 0:
            tool_params = Anthropic_NOT_GIVEN
        else:
            tool_params = [
                AnthropicToolParam(
                    input_schema=tool.input_schema,
                    name=tool.name,
                    description=tool.description,
                )
                for tool in tools
            ]

        response = None

        if thinking_tokens is None:
            thinking_tokens = self.thinking_tokens
        if thinking_tokens and thinking_tokens > 0:
            extra_body = {
                "thinking": {"type": "enabled", "budget_tokens": thinking_tokens}
            }
            temperature = 1
        else:
            extra_body = None

        for retry in range(self.max_retries):
            try:
                response = await self.async_client.messages.create(  # type: ignore
                    max_tokens=max_tokens,
                    messages=anthropic_messages,
                    model=self.model_name,
                    temperature=temperature,
                    system=system_prompt or Anthropic_NOT_GIVEN,
                    tool_choice=tool_choice_param,  # type: ignore
                    tools=tool_params,
                    extra_headers=self.headers,
                    extra_body=extra_body,
                )
                break
            except Exception as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Anthropic request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying LLM request: {retry + 1}/{self.max_retries}")
                    # Sleep 12-18 seconds with jitter to avoid thundering herd.
                    await asyncio.sleep(15 * random.uniform(0.8, 1.2))

        # Convert messages back to internal format
        internal_messages = []
        assert response is not None
        for message in response.content:
            if "</invoke>" in str(message):
                warning_msg = "\n".join(
                    ["!" * 80, "WARNING: Unexpected 'invoke' in message", "!" * 80]
                )
                print(warning_msg)

            if str(type(message)) == str(AnthropicTextBlock):
                message = cast(AnthropicTextBlock, message)
                internal_messages.append(TextResult(text=message.text))
            elif str(type(message)) == str(AnthropicRedactedThinkingBlock):
                # Convert Anthropic response back to internal format
                message = cast(AnthropicRedactedThinkingBlock, message)
                internal_messages.append(RedactedThinkingBlock(data=message.data))
            elif str(type(message)) == str(AnthropicThinkingBlock):
                # Convert Anthropic response back to internal format
                message = cast(AnthropicThinkingBlock, message)
                internal_messages.append(
                    ThinkingBlock(
                        thinking=message.thinking, signature=message.signature
                    )
                )
            elif str(type(message)) == str(AnthropicToolUseBlock):
                message = cast(AnthropicToolUseBlock, message)
                internal_messages.append(
                    ToolCall(
                        tool_call_id=message.id,
                        tool_name=message.name,
                        tool_input=recursively_remove_invoke_tag(message.input),
                    )
                )
            else:
                raise ValueError(f"Unknown message type: {type(message)}")

        cache_creation_input_tokens = getattr(
            response.usage, "cache_creation_input_tokens", 0
        )
        cache_read_input_tokens = getattr(response.usage, "cache_read_input_tokens", 0)
        input_tokens = (
            response.usage.input_tokens
            + cache_creation_input_tokens
            + cache_read_input_tokens
        )
        output_tokens = response.usage.output_tokens
        message_metadata = {
            "raw_response": response,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cache_creation_input_tokens": cache_creation_input_tokens,
            "cache_read_input_tokens": cache_read_input_tokens,
        }

        return internal_messages, message_metadata
