"""ContentPart type system for structured message content."""

from dataclasses import dataclass
from enum import Enum
from typing import Literal, Union, List, Optional
import base64


class MessageRole(str, Enum):
    """Message role enum."""

    ASSISTANT = "assistant"
    USER = "user"
    SYSTEM = "system"
    TOOL = "tool"


class FinishReason(str, Enum):
    """Reason why message generation finished."""

    END_TURN = "end_turn"
    MAX_TOKENS = "max_tokens"
    TOOL_USE = "tool_use"
    CANCELED = "canceled"
    ERROR = "error"
    PERMISSION_DENIED = "permission_denied"
    UNKNOWN = "unknown"


@dataclass
class TextContent:
    """Plain text content."""

    text: str
    type: Literal["text"] = "text"


@dataclass
class ReasoningContent:
    """Reasoning/thinking content from models like o1, o3-mini, Claude extended thinking."""

    thinking: str
    signature: str = ""
    started_at: Optional[int] = None
    finished_at: Optional[int] = None
    type: Literal["reasoning"] = "reasoning"


@dataclass
class ImageURLContent:
    """Image content with URL."""

    url: str
    detail: Optional[str] = None
    type: Literal["image_url"] = "image_url"


@dataclass
class BinaryContent:
    """Binary data (images, files) with base64 encoding."""

    path: str
    mime_type: str
    data: bytes
    type: Literal["binary"] = "binary"

    def to_base64(self, provider: str = "anthropic") -> str:
        """Convert to base64 string with provider-specific format."""
        encoded = base64.b64encode(self.data).decode("utf-8")
        if provider == "openai":
            return f"data:{self.mime_type};base64,{encoded}"
        return encoded


@dataclass
class ToolCall:
    """Tool/function call made by assistant."""

    id: str
    name: str
    input: str  # JSON string of parameters
    type: str = "function"
    finished: bool = True
    part_type: Literal["tool_call"] = "tool_call"


@dataclass
class ToolResult:
    """Result from tool execution."""

    tool_call_id: str
    name: str
    content: str
    metadata: str = ""
    is_error: bool = False
    type: Literal["tool_result"] = "tool_result"


@dataclass
class Finish:
    """Message completion marker with finish reason."""

    reason: FinishReason
    time: int
    message: str = ""
    details: str = ""
    type: Literal["finish"] = "finish"


# Union type for all content parts
ContentPart = Union[
    TextContent,
    ReasoningContent,
    ImageURLContent,
    BinaryContent,
    ToolCall,
    ToolResult,
    Finish,
]


@dataclass
class Message:
    """Strongly-typed message with ContentPart list."""

    id: str
    role: MessageRole
    session_id: str
    parts: List[ContentPart]
    model: str = ""
    provider: str = ""
    created_at: int = 0
    updated_at: int = 0

    def content(self) -> Optional[TextContent]:
        """Extract first text content part."""
        for part in self.parts:
            if isinstance(part, TextContent):
                return part
        return None

    def tool_calls(self) -> List[ToolCall]:
        """Extract all tool call parts."""
        return [p for p in self.parts if isinstance(p, ToolCall)]

    def tool_results(self) -> List[ToolResult]:
        """Extract all tool result parts."""
        return [p for p in self.parts if isinstance(p, ToolResult)]

    def reasoning(self) -> Optional[ReasoningContent]:
        """Extract reasoning content if present."""
        for part in self.parts:
            if isinstance(part, ReasoningContent):
                return part
        return None


@dataclass
class Session:
    """Session model with token tracking and context window management."""

    id: str
    title: str
    user_id: str
    parent_session_id: str = ""
    message_count: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0
    summary_message_id: str = ""
    cost: float = 0.0
    created_at: int = 0
    updated_at: int = 0
