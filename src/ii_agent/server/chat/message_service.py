"""Message service with ContentPart serialization/deserialization."""

import json
import uuid
import time
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ii_agent.db.chat import ChatMessage
from ii_agent.server.chat.content_parts import (
    Message,
    MessageRole,
    ContentPart,
    TextContent,
    ReasoningContent,
    ImageURLContent,
    BinaryContent,
    ToolCall,
    ToolResult,
    Finish,
    FinishReason,
)


class MessageService:
    """Service for managing messages with ContentPart serialization."""

    # Type map for deserialization
    TYPE_MAP = {
        "text": TextContent,
        "reasoning": ReasoningContent,
        "image_url": ImageURLContent,
        "binary": BinaryContent,
        "tool_call": <PERSON><PERSON><PERSON>all,
        "tool_result": Tool<PERSON>esult,
        "finish": Finish,
    }

    @classmethod
    def serialize_parts(cls, parts: List[ContentPart]) -> str:
        """Serialize ContentParts to JSON with type tags."""
        wrapped_parts = []
        for part in parts:
            # Get the type field from the dataclass
            part_type = getattr(part, "type", getattr(part, "part_type", None))
            part_dict = {
                "type": part_type,
                "data": {
                    k: v
                    for k, v in part.__dict__.items()
                    if k not in ["type", "part_type"]
                },
            }
            wrapped_parts.append(part_dict)
        return json.dumps(wrapped_parts)

    @classmethod
    def deserialize_parts(cls, parts_json: str) -> List[ContentPart]:
        """Deserialize ContentParts from JSON."""
        if not parts_json:
            return []

        wrapped_parts = json.loads(parts_json)
        parts = []

        for wrapped in wrapped_parts:
            part_type = wrapped["type"]
            part_data = wrapped["data"]

            if part_type in cls.TYPE_MAP:
                part_class = cls.TYPE_MAP[part_type]
                # Handle FinishReason enum for Finish parts
                if part_type == "finish" and "reason" in part_data:
                    part_data["reason"] = FinishReason(part_data["reason"])
                # Handle bytes for BinaryContent
                if part_type == "binary" and "data" in part_data:
                    if isinstance(part_data["data"], str):
                        import base64

                        part_data["data"] = base64.b64decode(part_data["data"])

                part = part_class(**part_data)
                parts.append(part)

        return parts

    @classmethod
    async def create_message(
        cls,
        db_session: AsyncSession,
        session_id: str,
        role: MessageRole,
        parts: List[ContentPart],
        model: str = "",
        provider: str = "",
    ) -> Message:
        """Create a new message with ContentParts."""
        message_id = str(uuid.uuid4())
        now = int(time.time())
        parts_json = cls.serialize_parts(parts)

        # Store in database with JSONB content
        # For backward compatibility, also create a simple dict representation
        db_message = ChatMessage(
            id=uuid.UUID(message_id),
            session_id=session_id,
            role=role.value,
            content={"parts_json": parts_json},  # Store serialized parts
            model=model or None,
            is_finished=True,
        )
        db_session.add(db_message)
        await db_session.commit()
        await db_session.refresh(db_message)

        return Message(
            id=message_id,
            role=role,
            session_id=session_id,
            parts=parts,
            model=model,
            provider=provider,
            created_at=now,
            updated_at=now,
        )

    @classmethod
    async def list_by_session(
        cls, db_session: AsyncSession, session_id: str, limit: int = 50
    ) -> List[Message]:
        """List messages for a session with ContentPart deserialization."""
        result = await db_session.execute(
            select(ChatMessage)
            .where(ChatMessage.session_id == session_id)
            .order_by(ChatMessage.created_at.asc())
            .limit(limit)
        )
        db_messages = result.scalars().all()

        messages = []
        for db_msg in db_messages:
            # Extract parts_json from content
            parts_json = db_msg.content.get("parts_json", "[]")
            parts = cls.deserialize_parts(parts_json)

            message = Message(
                id=str(db_msg.id),
                role=MessageRole(db_msg.role),
                session_id=db_msg.session_id,
                parts=parts,
                model=db_msg.model or "",
                provider="",
                created_at=int(db_msg.created_at.timestamp()),
                updated_at=int(db_msg.updated_at.timestamp()),
            )
            messages.append(message)

        return messages
