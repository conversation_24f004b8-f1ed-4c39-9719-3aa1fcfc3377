"""Provider interface definitions matching research architecture."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import AsyncIterator, List, Optional, Dict, Any

from ii_agent.server.chat.content_parts import (
    Message,
    ToolCall,
    FinishReason,
)


@dataclass
class TokenUsage:
    """Token usage statistics."""

    input_tokens: int = 0
    output_tokens: int = 0
    cache_creation_tokens: int = 0
    cache_read_tokens: int = 0


@dataclass
class ProviderResponse:
    """Complete response from provider."""

    content: str
    tool_calls: List[ToolCall]
    usage: TokenUsage
    finish_reason: FinishReason


class EventType(str, Enum):
    """Granular event types for streaming."""

    CONTENT_START = "content_start"
    CONTENT_DELTA = "content_delta"
    CONTENT_STOP = "content_stop"
    TOOL_USE_START = "tool_use_start"
    TOOL_USE_DELTA = "tool_use_delta"
    TOOL_USE_STOP = "tool_use_stop"
    THINKING_DELTA = "thinking_delta"
    SIGNATURE_DELTA = "signature_delta"
    COMPLETE = "complete"
    ERROR = "error"
    WARNING = "warning"


@dataclass
class ProviderEvent:
    """Event emitted during streaming."""

    type: EventType
    content: str = ""
    thinking: str = ""
    signature: str = ""
    response: Optional[ProviderResponse] = None
    tool_call: Optional[ToolCall] = None
    error: Optional[Exception] = None


class ProviderClient(ABC):
    """Abstract provider client interface."""

    @abstractmethod
    async def send(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> ProviderResponse:
        """Send messages and get complete response."""
        pass

    @abstractmethod
    async def stream(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> AsyncIterator[ProviderEvent]:
        """Stream response events."""
        pass

    @abstractmethod
    def model(self) -> Dict[str, Any]:
        """Get model metadata."""
        pass
