"""Anthropic provider using official SDK."""

import logging
import json
from typing import AsyncItera<PERSON>, List, Optional, Dict, Any

import anthropic
from anthropic.types import <PERSON><PERSON><PERSON>, ToolUseBlock

from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.server.chat.provider_interface import (
    ProviderClient,
    ProviderEvent,
    ProviderResponse,
    TokenUsage,
    EventType,
)
from ii_agent.server.chat.content_parts import (
    Message,
    MessageRole,
    ToolCall,
    FinishReason,
)

logger = logging.getLogger(__name__)


class AnthropicProvider(ProviderClient):
    """Provider for Anthropic Claude models using official SDK."""

    def __init__(self, llm_config: LLMConfig):
        """Initialize Anthropic provider."""
        self.llm_config = llm_config
        self.model_name = llm_config.model

        # Initialize client (Vertex or standard)
        if llm_config.vertex_project_id and llm_config.vertex_region:
            self.client = anthropic.AsyncAnthropicVertex(
                project_id=llm_config.vertex_project_id,
                region=llm_config.vertex_region,
                timeout=60 * 5,
                max_retries=1,
            )
        else:
            self.client = anthropic.AsyncAnthropic(
                api_key=llm_config.api_key.get_secret_value()
                if llm_config.api_key
                else None,
                timeout=60 * 5,
                max_retries=1,
            )

    def _convert_messages(
        self, messages: List[Message]
    ) -> tuple[str, List[Dict[str, Any]]]:
        """Convert Message objects to Anthropic format.

        Returns:
            Tuple of (system_prompt, anthropic_messages)
        """
        system_prompt = ""
        anthropic_messages = []

        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                # Extract system message
                text_part = msg.content()
                if text_part:
                    system_prompt = text_part.text

            elif msg.role == MessageRole.USER:
                content = []
                # Add text content
                text_part = msg.content()
                if text_part:
                    content.append({"type": "text", "text": text_part.text})

                # Add images
                for part in msg.parts:
                    if hasattr(part, "mime_type"):  # BinaryContent
                        content.append(
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": part.mime_type,
                                    "data": part.to_base64("anthropic"),
                                },
                            }
                        )
                    elif hasattr(part, "url"):  # ImageURLContent
                        content.append(
                            {
                                "type": "image",
                                "source": {"type": "url", "url": part.url},
                            }
                        )

                # Add tool results
                for result in msg.tool_results():
                    content.append(
                        {
                            "type": "tool_result",
                            "tool_use_id": result.tool_call_id,
                            "content": result.content,
                            "is_error": result.is_error,
                        }
                    )

                anthropic_messages.append({"role": "user", "content": content})

            elif msg.role == MessageRole.ASSISTANT:
                content = []

                # Add text content
                text_part = msg.content()
                if text_part:
                    content.append({"type": "text", "text": text_part.text})

                # Add tool calls (only finished ones)
                for tool_call in msg.tool_calls():
                    if tool_call.finished:
                        # Parse JSON input string to dict
                        try:
                            input_dict = (
                                json.loads(tool_call.input)
                                if isinstance(tool_call.input, str)
                                else tool_call.input
                            )
                        except json.JSONDecodeError:
                            input_dict = {}

                        content.append(
                            {
                                "type": "tool_use",
                                "id": tool_call.id,
                                "name": tool_call.name,
                                "input": input_dict,
                            }
                        )

                if content:
                    anthropic_messages.append({"role": "assistant", "content": content})

        return system_prompt, anthropic_messages

    async def send(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> ProviderResponse:
        """Send messages and get complete response."""
        system_prompt, anthropic_messages = self._convert_messages(messages)

        params = {
            "model": self.model_name,
            "messages": anthropic_messages,
            "max_tokens": 4096,
        }

        if system_prompt:
            params["system"] = system_prompt

        if self.llm_config.temperature is not None:
            params["temperature"] = self.llm_config.temperature

        response = await self.client.messages.create(**params)

        # Extract content
        content_text = ""
        tool_calls = []

        for block in response.content:
            if isinstance(block, TextBlock):
                content_text += block.text
            elif isinstance(block, ToolUseBlock):
                tool_calls.append(
                    ToolCall(
                        id=block.id,
                        name=block.name,
                        input=json.dumps(block.input),  # Convert dict to JSON string
                        finished=True,
                    )
                )

        # Extract usage
        usage = TokenUsage(
            input_tokens=response.usage.input_tokens,
            output_tokens=response.usage.output_tokens,
            cache_creation_tokens=getattr(
                response.usage, "cache_creation_input_tokens", 0
            ),
            cache_read_tokens=getattr(response.usage, "cache_read_input_tokens", 0),
        )

        # Map stop reason
        finish_reason_map = {
            "end_turn": FinishReason.END_TURN,
            "max_tokens": FinishReason.MAX_TOKENS,
            "tool_use": FinishReason.TOOL_USE,
            "stop_sequence": FinishReason.END_TURN,
        }
        finish_reason = finish_reason_map.get(
            response.stop_reason, FinishReason.UNKNOWN
        )

        return ProviderResponse(
            content=content_text,
            tool_calls=tool_calls,
            usage=usage,
            finish_reason=finish_reason,
        )

    async def stream(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> AsyncIterator[ProviderEvent]:
        """Stream response with granular events."""
        system_prompt, anthropic_messages = self._convert_messages(messages)

        params = {
            "model": self.model_name,
            "messages": anthropic_messages,
            "max_tokens": 4096,
        }

        if system_prompt:
            params["system"] = system_prompt

        if self.llm_config.temperature is not None:
            params["temperature"] = self.llm_config.temperature

        accumulated_content = ""
        accumulated_tool_calls = {}
        content_started = False

        async with self.client.messages.stream(**params) as stream:
            async for event in stream:
                # Content block start
                if event.type == "content_block_start":
                    if hasattr(event, "content_block"):
                        if event.content_block.type == "text":
                            if not content_started:
                                yield ProviderEvent(type=EventType.CONTENT_START)
                                content_started = True
                        elif event.content_block.type == "tool_use":
                            tool_call = ToolCall(
                                id=event.content_block.id,
                                name=event.content_block.name,
                                input="",
                                finished=False,
                            )
                            accumulated_tool_calls[event.content_block.id] = tool_call
                            yield ProviderEvent(
                                type=EventType.TOOL_USE_START, tool_call=tool_call
                            )

                # Content block delta
                elif event.type == "content_block_delta":
                    if hasattr(event, "delta"):
                        if event.delta.type == "text_delta":
                            accumulated_content += event.delta.text
                            yield ProviderEvent(
                                type=EventType.CONTENT_DELTA, content=event.delta.text
                            )
                        elif event.delta.type == "input_json_delta":
                            # Tool call input delta
                            if hasattr(event, "index"):
                                # Find tool call by index
                                tool_calls_list = list(accumulated_tool_calls.values())
                                if event.index < len(tool_calls_list):
                                    tool_call = tool_calls_list[event.index]
                                    tool_call.input += event.delta.partial_json
                                    yield ProviderEvent(
                                        type=EventType.TOOL_USE_DELTA,
                                        tool_call=ToolCall(
                                            id=tool_call.id,
                                            name=tool_call.name,
                                            input=event.delta.partial_json,
                                            finished=False,
                                        ),
                                    )

                # Content block stop
                elif event.type == "content_block_stop":
                    if content_started:
                        yield ProviderEvent(type=EventType.CONTENT_STOP)
                        content_started = False
                    # Mark tool calls as finished
                    for tool_call in accumulated_tool_calls.values():
                        tool_call.finished = True
                        yield ProviderEvent(
                            type=EventType.TOOL_USE_STOP, tool_call=tool_call
                        )

                # Message complete
                elif event.type == "message_stop":
                    # Get final message
                    message = await stream.get_final_message()

                    usage = TokenUsage(
                        input_tokens=message.usage.input_tokens,
                        output_tokens=message.usage.output_tokens,
                        cache_creation_tokens=getattr(
                            message.usage, "cache_creation_input_tokens", 0
                        ),
                        cache_read_tokens=getattr(
                            message.usage, "cache_read_input_tokens", 0
                        ),
                    )

                    finish_reason_map = {
                        "end_turn": FinishReason.END_TURN,
                        "max_tokens": FinishReason.MAX_TOKENS,
                        "tool_use": FinishReason.TOOL_USE,
                        "stop_sequence": FinishReason.END_TURN,
                    }
                    finish_reason = finish_reason_map.get(
                        message.stop_reason, FinishReason.UNKNOWN
                    )

                    yield ProviderEvent(
                        type=EventType.COMPLETE,
                        response=ProviderResponse(
                            content=accumulated_content,
                            tool_calls=list(accumulated_tool_calls.values()),
                            usage=usage,
                            finish_reason=finish_reason,
                        ),
                    )

    def model(self) -> Dict[str, Any]:
        """Get model metadata."""
        return {"id": self.model_name, "name": self.model_name}
