"""Gemini provider using official Google GenAI SDK."""

import logging
import json
from typing import AsyncItera<PERSON>, List, Optional, Dict, Any

from google import genai
from google.genai import types

from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.server.chat.provider_interface import (
    ProviderClient,
    ProviderEvent,
    ProviderResponse,
    TokenUsage,
    EventType,
)
from ii_agent.server.chat.content_parts import (
    Message,
    MessageRole,
    ToolCall,
    FinishReason,
)

logger = logging.getLogger(__name__)


class GeminiProvider(ProviderClient):
    """Provider for Google Gemini models using official SDK."""

    def __init__(self, llm_config: LLMConfig):
        """Initialize Gemini provider."""
        self.llm_config = llm_config
        self.model_name = llm_config.model

        # Initialize client
        api_key = llm_config.api_key.get_secret_value() if llm_config.api_key else None
        self.client = genai.Client(api_key=api_key)

    def _convert_messages(self, messages: List[Message]) -> List[types.Content]:
        """Convert Message objects to Gemini format."""
        gemini_messages = []

        for msg in messages:
            parts = []

            if msg.role in [MessageRole.USER, MessageRole.SYSTEM]:
                # Add text content
                text_part = msg.content()
                if text_part:
                    parts.append(types.Part(text=text_part.text))

                # Add images (inline binary data for Gemini)
                for part in msg.parts:
                    if hasattr(part, "mime_type"):  # BinaryContent
                        parts.append(
                            types.Part(
                                inline_data=types.Blob(
                                    mime_type=part.mime_type, data=part.data
                                )
                            )
                        )

                role_map = {MessageRole.USER: "user", MessageRole.SYSTEM: "user"}
                gemini_messages.append(
                    types.Content(role=role_map[msg.role], parts=parts)
                )

            elif msg.role == MessageRole.ASSISTANT:
                # Add text content
                text_part = msg.content()
                if text_part:
                    parts.append(types.Part(text=text_part.text))

                # Add function calls (only finished ones)
                for tool_call in msg.tool_calls():
                    if tool_call.finished:
                        # Parse JSON input
                        try:
                            args_dict = (
                                json.loads(tool_call.input)
                                if isinstance(tool_call.input, str)
                                else tool_call.input
                            )
                        except json.JSONDecodeError:
                            args_dict = {}

                        parts.append(
                            types.Part(
                                function_call=types.FunctionCall(
                                    name=tool_call.name, args=args_dict
                                )
                            )
                        )

                if parts:
                    gemini_messages.append(types.Content(role="model", parts=parts))

            elif msg.role == MessageRole.TOOL:
                # Function responses
                for result in msg.tool_results():
                    parts.append(
                        types.Part(
                            function_response=types.FunctionResponse(
                                name=result.name, response={"result": result.content}
                            )
                        )
                    )

                if parts:
                    gemini_messages.append(types.Content(role="function", parts=parts))

        return gemini_messages

    async def send(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> ProviderResponse:
        """Send messages and get complete response."""
        gemini_messages = self._convert_messages(messages)

        config = types.GenerateContentConfig()
        if self.llm_config.temperature is not None:
            config.temperature = self.llm_config.temperature

        response = await self.client.aio.models.generate_content(
            model=self.model_name, contents=gemini_messages, config=config
        )

        # Extract content
        content_text = ""
        tool_calls = []

        if response.candidates and response.candidates[0].content:
            for part in response.candidates[0].content.parts:
                if part.text:
                    content_text += part.text
                elif part.function_call:
                    tool_calls.append(
                        ToolCall(
                            id=f"call_{part.function_call.name}",  # Gemini doesn't provide IDs
                            name=part.function_call.name,
                            input=json.dumps(part.function_call.args),
                            finished=True,
                        )
                    )

        # Extract usage
        usage = TokenUsage(
            input_tokens=response.usage_metadata.prompt_token_count
            if response.usage_metadata
            else 0,
            output_tokens=response.usage_metadata.candidates_token_count
            if response.usage_metadata
            else 0,
            cache_creation_tokens=0,
            cache_read_tokens=response.usage_metadata.cached_content_token_count
            if response.usage_metadata
            else 0,
        )

        # Map finish reason
        finish_reason = FinishReason.UNKNOWN
        if response.candidates and response.candidates[0].finish_reason:
            reason_map = {
                "STOP": FinishReason.END_TURN,
                "MAX_TOKENS": FinishReason.MAX_TOKENS,
                "SAFETY": FinishReason.ERROR,
                "RECITATION": FinishReason.ERROR,
            }
            finish_reason = reason_map.get(
                response.candidates[0].finish_reason, FinishReason.UNKNOWN
            )

        return ProviderResponse(
            content=content_text,
            tool_calls=tool_calls,
            usage=usage,
            finish_reason=finish_reason,
        )

    async def stream(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> AsyncIterator[ProviderEvent]:
        """Stream response with granular events."""
        gemini_messages = self._convert_messages(messages)

        config = types.GenerateContentConfig()
        if self.llm_config.temperature is not None:
            config.temperature = self.llm_config.temperature

        stream = await self.client.aio.models.generate_content_stream(
            model=self.model_name, contents=gemini_messages, config=config
        )

        accumulated_content = ""
        accumulated_tool_calls = {}
        content_started = False

        async for chunk in stream:
            if not chunk.candidates or not chunk.candidates[0].content:
                continue

            for part in chunk.candidates[0].content.parts:
                # Handle text content
                if part.text:
                    if not content_started:
                        yield ProviderEvent(type=EventType.CONTENT_START)
                        content_started = True

                    accumulated_content += part.text
                    yield ProviderEvent(type=EventType.CONTENT_DELTA, content=part.text)

                # Handle function calls
                elif part.function_call:
                    call_id = f"call_{part.function_call.name}"
                    if call_id not in accumulated_tool_calls:
                        tool_call = ToolCall(
                            id=call_id,
                            name=part.function_call.name,
                            input="",
                            finished=False,
                        )
                        accumulated_tool_calls[call_id] = tool_call
                        yield ProviderEvent(
                            type=EventType.TOOL_USE_START, tool_call=tool_call
                        )

                    # Accumulate args
                    tool_call = accumulated_tool_calls[call_id]
                    tool_call.input = json.dumps(part.function_call.args)
                    yield ProviderEvent(
                        type=EventType.TOOL_USE_DELTA,
                        tool_call=ToolCall(
                            id=call_id,
                            name=part.function_call.name,
                            input=json.dumps(part.function_call.args),
                            finished=False,
                        ),
                    )

            # Check for completion (last chunk)
            if chunk.candidates[0].finish_reason:
                if content_started:
                    yield ProviderEvent(type=EventType.CONTENT_STOP)

                # Mark tool calls as finished
                for tool_call in accumulated_tool_calls.values():
                    tool_call.finished = True
                    yield ProviderEvent(
                        type=EventType.TOOL_USE_STOP, tool_call=tool_call
                    )

                # Extract usage
                usage = TokenUsage(
                    input_tokens=chunk.usage_metadata.prompt_token_count
                    if chunk.usage_metadata
                    else 0,
                    output_tokens=chunk.usage_metadata.candidates_token_count
                    if chunk.usage_metadata
                    else 0,
                    cache_creation_tokens=0,
                    cache_read_tokens=chunk.usage_metadata.cached_content_token_count
                    if chunk.usage_metadata
                    else 0,
                )

                # Map finish reason
                reason_map = {
                    "STOP": FinishReason.END_TURN,
                    "MAX_TOKENS": FinishReason.MAX_TOKENS,
                    "SAFETY": FinishReason.ERROR,
                    "RECITATION": FinishReason.ERROR,
                }
                finish_reason = reason_map.get(
                    chunk.candidates[0].finish_reason, FinishReason.UNKNOWN
                )

                yield ProviderEvent(
                    type=EventType.COMPLETE,
                    response=ProviderResponse(
                        content=accumulated_content,
                        tool_calls=list(accumulated_tool_calls.values()),
                        usage=usage,
                        finish_reason=finish_reason,
                    ),
                )

    def model(self) -> Dict[str, Any]:
        """Get model metadata."""
        return {"id": self.model_name, "name": self.model_name}
