"""OpenAI provider using official SDK."""

import logging
from typing import AsyncIterator, List, Optional, Dict, Any

import openai

from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.server.chat.provider_interface import (
    ProviderClient,
    ProviderEvent,
    ProviderResponse,
    TokenUsage,
    EventType,
)
from ii_agent.server.chat.content_parts import (
    Message,
    MessageRole,
    ToolCall,
    FinishReason,
)

logger = logging.getLogger(__name__)


class OpenAIProvider(ProviderClient):
    """Provider for OpenAI models using official SDK."""

    def __init__(self, llm_config: LLMConfig):
        """Initialize OpenAI provider."""
        self.llm_config = llm_config
        self.model_name = llm_config.model

        api_key = llm_config.api_key.get_secret_value() if llm_config.api_key else None

        # Initialize client (Azure or standard)
        if llm_config.azure_endpoint:
            self.client = openai.AsyncAzureOpenAI(
                api_key=api_key,
                azure_endpoint=llm_config.azure_endpoint,
                api_version=llm_config.azure_api_version,
                max_retries=1,
            )
        else:
            base_url = llm_config.base_url or "https://api.openai.com/v1"
            self.client = openai.AsyncOpenAI(
                api_key=api_key,
                base_url=base_url,
                max_retries=1,
            )

    def _convert_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert Message objects to OpenAI format."""
        openai_messages = []

        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                text_part = msg.content()
                if text_part:
                    openai_messages.append(
                        {"role": "system", "content": text_part.text}
                    )

            elif msg.role == MessageRole.USER:
                content = []
                # Add text content
                text_part = msg.content()
                if text_part:
                    content.append({"type": "text", "text": text_part.text})

                # Add images
                for part in msg.parts:
                    if hasattr(part, "mime_type"):  # BinaryContent
                        content.append(
                            {
                                "type": "image_url",
                                "image_url": {"url": part.to_base64("openai")},
                            }
                        )
                    elif hasattr(part, "url"):  # ImageURLContent
                        content.append(
                            {"type": "image_url", "image_url": {"url": part.url}}
                        )

                openai_messages.append(
                    {
                        "role": "user",
                        "content": content
                        if len(content) > 1
                        else (text_part.text if text_part else ""),
                    }
                )

            elif msg.role == MessageRole.ASSISTANT:
                message = {"role": "assistant"}

                # Add text content
                text_part = msg.content()
                if text_part:
                    message["content"] = text_part.text

                # Add tool calls (only finished ones)
                tool_calls = [tc for tc in msg.tool_calls() if tc.finished]
                if tool_calls:
                    message["tool_calls"] = [
                        {
                            "id": tc.id,
                            "type": "function",
                            "function": {"name": tc.name, "arguments": tc.input},
                        }
                        for tc in tool_calls
                    ]

                if message.get("content") or message.get("tool_calls"):
                    openai_messages.append(message)

            elif msg.role == MessageRole.TOOL:
                # Tool result messages
                for result in msg.tool_results():
                    openai_messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": result.tool_call_id,
                            "content": result.content,
                        }
                    )

        return openai_messages

    async def send(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> ProviderResponse:
        """Send messages and get complete response."""
        openai_messages = self._convert_messages(messages)

        params = {
            "model": self.model_name,
            "messages": openai_messages,
        }

        if self.llm_config.temperature is not None:
            params["temperature"] = self.llm_config.temperature

        response = await self.client.chat.completions.create(**params)

        # Extract response data
        choice = response.choices[0]
        message = choice.message

        # Extract tool calls
        tool_calls = []
        if message.tool_calls:
            tool_calls = [
                ToolCall(
                    id=tc.id,
                    name=tc.function.name,
                    input=tc.function.arguments,
                    finished=True,
                )
                for tc in message.tool_calls
            ]

        # Extract usage
        usage = TokenUsage(
            input_tokens=response.usage.prompt_tokens,
            output_tokens=response.usage.completion_tokens,
            cache_creation_tokens=getattr(
                response.usage, "prompt_tokens_details", {}
            ).get("cached_tokens", 0)
            if hasattr(response.usage, "prompt_tokens_details")
            else 0,
            cache_read_tokens=0,
        )

        # Map finish reason
        finish_reason_map = {
            "stop": FinishReason.END_TURN,
            "length": FinishReason.MAX_TOKENS,
            "tool_calls": FinishReason.TOOL_USE,
            "content_filter": FinishReason.ERROR,
        }
        finish_reason = finish_reason_map.get(
            choice.finish_reason, FinishReason.UNKNOWN
        )

        return ProviderResponse(
            content=message.content or "",
            tool_calls=tool_calls,
            usage=usage,
            finish_reason=finish_reason,
        )

    async def stream(
        self, messages: List[Message], tools: Optional[List[Any]] = None
    ) -> AsyncIterator[ProviderEvent]:
        """Stream response with granular events."""
        openai_messages = self._convert_messages(messages)

        params = {
            "model": self.model_name,
            "messages": openai_messages,
            "stream": True,
            "stream_options": {"include_usage": True},
        }

        # SKIP for now , gpt-5 model does not support temperature
        # if self.llm_config.temperature is not None:
        #     params["temperature"] = self.llm_config.temperature

        stream = await self.client.chat.completions.create(**params)

        accumulated_content = ""
        accumulated_tool_calls = {}
        content_started = False

        async for chunk in stream:
            if not chunk.choices:
                continue

            delta = chunk.choices[0].delta

            # Handle content delta
            if delta.content:
                if not content_started:
                    yield ProviderEvent(type=EventType.CONTENT_START)
                    content_started = True

                accumulated_content += delta.content
                yield ProviderEvent(type=EventType.CONTENT_DELTA, content=delta.content)

            # Handle tool call deltas
            if delta.tool_calls:
                for tc_delta in delta.tool_calls:
                    tc_index = tc_delta.index
                    if tc_index not in accumulated_tool_calls:
                        accumulated_tool_calls[tc_index] = ToolCall(
                            id=tc_delta.id or "", name="", input="", finished=False
                        )
                        yield ProviderEvent(
                            type=EventType.TOOL_USE_START,
                            tool_call=accumulated_tool_calls[tc_index],
                        )

                    tool_call = accumulated_tool_calls[tc_index]

                    if tc_delta.id:
                        tool_call.id = tc_delta.id

                    if tc_delta.function:
                        if tc_delta.function.name:
                            tool_call.name = tc_delta.function.name

                        if tc_delta.function.arguments:
                            tool_call.input += tc_delta.function.arguments
                            yield ProviderEvent(
                                type=EventType.TOOL_USE_DELTA,
                                tool_call=ToolCall(
                                    id=tool_call.id,
                                    name=tool_call.name,
                                    input=tc_delta.function.arguments,
                                    finished=False,
                                ),
                            )

            # Handle completion
            if chunk.choices[0].finish_reason:
                if content_started:
                    yield ProviderEvent(type=EventType.CONTENT_STOP)

                # Mark tool calls as finished
                for tool_call in accumulated_tool_calls.values():
                    tool_call.finished = True
                    yield ProviderEvent(
                        type=EventType.TOOL_USE_STOP, tool_call=tool_call
                    )

                # Extract usage (from final chunk)
                usage = TokenUsage()
                if chunk.usage:
                    usage = TokenUsage(
                        input_tokens=chunk.usage.prompt_tokens,
                        output_tokens=chunk.usage.completion_tokens,
                        cache_creation_tokens=getattr(
                            chunk.usage, "prompt_tokens_details", {}
                        ).get("cached_tokens", 0)
                        if hasattr(chunk.usage, "prompt_tokens_details")
                        else 0,
                        cache_read_tokens=0,
                    )

                # Map finish reason
                finish_reason_map = {
                    "stop": FinishReason.END_TURN,
                    "length": FinishReason.MAX_TOKENS,
                    "tool_calls": FinishReason.TOOL_USE,
                }
                finish_reason = finish_reason_map.get(
                    chunk.choices[0].finish_reason, FinishReason.UNKNOWN
                )

                yield ProviderEvent(
                    type=EventType.COMPLETE,
                    response=ProviderResponse(
                        content=accumulated_content,
                        tool_calls=list(accumulated_tool_calls.values()),
                        usage=usage,
                        finish_reason=finish_reason,
                    ),
                )

    def model(self) -> Dict[str, Any]:
        """Get model metadata."""
        return {"id": self.model_name, "name": self.model_name}
