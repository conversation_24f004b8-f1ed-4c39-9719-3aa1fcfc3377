"""Pydantic models for chat API requests/responses.

Message Format Documentation
============================

This module defines message formats compatible with all major LLM providers:
- OpenAI (GPT-4, GPT-3.5, o1)
- Anthropic (<PERSON> 3, <PERSON> 3.5)
- Google (Gemini)

Message Types Stored in Database
---------------------------------

1. User Messages:
   {
       "role": "user",
       "content": "text content",
       "tool_calls": null,
       "function_call": null
   }

2. Assistant Messages (Text):
   {
       "role": "assistant",
       "content": "response text",
       "tool_calls": null,
       "function_call": null,
       "reasoning_content": null  # For o1 models
   }

3. Assistant Messages (with Tool Calls):
   {
       "role": "assistant",
       "content": null,  # Can be null when making tool calls
       "tool_calls": [
           {
               "id": "call_123",
               "type": "function",
               "function": {
                   "name": "get_weather",
                   "arguments": "{\"location\": \"SF\"}"
               }
           }
       ],
       "function_call": null,
       "reasoning_content": null
   }

4. Tool Response Messages:
   {
       "role": "tool",
       "content": "tool result",
       "tool_call_id": "call_123",
       "name": "get_weather"
   }

5. Assistant Messages (with Reasoning):
   {
       "role": "assistant",
       "content": "final answer",
       "tool_calls": null,
       "function_call": null,
       "reasoning_content": "thinking process..."  # OpenAI o1, Claude extended thinking
   }
"""

from typing import List, Optional, Union, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class ChatMessageRequest(BaseModel):
    """Request to send a chat message."""

    content: Union[str, Dict[str, Any]] = Field(
        ..., description="Message content - can be string or JSON object"
    )
    model_id: str = Field(..., description="LLM model ID")
    files: Optional[List[str]] = Field(
        None, description="List of file paths to include in the message"
    )
    session_id: Optional[UUID] = Field(None, description="Existing session ID")


class StopConversationRequest(BaseModel):
    """Request to send a chat message."""

    session_id: UUID = Field(..., description="Existing session ID")


class StopConversationResponse(BaseModel):
    """Request to send a chat message."""

    success: bool = Field(
        ..., description="Whether the conversation was successfully stopped"
    )
    last_message_id: Optional[UUID] = Field(
        None, description="ID of the last message in the conversation"
    )


class ToolCall(BaseModel):
    """Tool call object (OpenAI format)."""

    id: Optional[str] = None
    type: str = "function"
    function: Dict[str, Any]  # {name: str, arguments: str}


class FunctionCall(BaseModel):
    """Legacy function call object (OpenAI format)."""

    name: str
    arguments: str


class MessageObject(BaseModel):
    """OpenAI-compatible message object.

    This represents a message stored in the database and can be used
    to reconstruct the conversation history for LLM requests.

    Supports all LLM providers (OpenAI, Anthropic, Gemini, etc.)
    """

    content: Optional[str] = None  # Main text content
    role: str  # "user", "assistant", "system", "tool"
    tool_calls: Optional[List[ToolCall]] = None  # Tool calls made by assistant
    function_call: Optional[FunctionCall] = None  # Legacy function call
    reasoning_content: Optional[str] = (
        None  # Reasoning/thinking content (o1, Claude thinking)
    )
    tool_call_id: Optional[str] = None  # For tool response messages
    name: Optional[str] = None  # Tool/function name for tool messages
    provider_specific_fields: Optional[Dict[str, Any]] = None  # Provider-specific data


class UsageObject(BaseModel):
    """Token usage statistics."""

    completion_tokens: int
    prompt_tokens: int
    total_tokens: int
    completion_tokens_details: Optional[Dict[str, Any]] = None
    prompt_tokens_details: Optional[Dict[str, Any]] = None


class ChatMessageResponse(BaseModel):
    """Single chat message response."""

    id: str
    role: str  # "user" or "assistant"
    message: Union[MessageObject, Dict[str, Any]]  # Full message object
    usage: Optional[Union[UsageObject, Dict[str, Any]]] = None  # Usage statistics
    tokens: Optional[int] = None  # Total accumulated tokens
    model: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class MessageHistoryResponse(BaseModel):
    """Response with message history."""

    messages: List[ChatMessageResponse]
    has_more: bool
    total_count: int


class ClearHistoryResponse(BaseModel):
    """Response after clearing history."""

    success: bool
    deleted_count: int
    message: str


class ModelInfo(BaseModel):
    """Information about an available model."""

    id: str
    name: str
    provider: str
    cost_per_1k_tokens: float
    max_tokens: int
    supports_streaming: bool


class ModelsListResponse(BaseModel):
    """Response with list of available models."""

    models: List[ModelInfo]


class SessionMetadata(BaseModel):
    """Chat session metadata."""

    session_id: str = Field(..., description="Unique session identifier")
    name: str = Field(..., description="Session name")
    status: str = Field(..., description="Session status (active, completed, etc.)")
    agent_type: str = Field(..., description="Type of agent (chat)")
    model_id: str = Field(..., description="LLM model ID used for this session")
    created_at: str = Field(..., description="Session creation timestamp (ISO 8601)")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "550e8400-e29b-41d4-a716-446655440000",
                "name": "Chat - 2025-10-23 15:30",
                "status": "active",
                "agent_type": "chat",
                "model_id": "gpt-4",
                "created_at": "2025-10-23T15:30:00.000Z",
            }
        }
