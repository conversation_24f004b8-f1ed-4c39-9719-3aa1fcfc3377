"""Chat service with LiteLLM integration."""

import logging
import uuid
from typing import Async<PERSON><PERSON><PERSON>, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from ii_agent.db.chat import ChatMessage
from ii_agent.db.models import Session
from ii_agent.server.chat.llm import LLMProviderFactory
from ii_agent.server.chat.provider_interface import TokenUsage
from ii_agent.server.llm_settings.service import (
    get_user_llm_config,
    get_system_llm_config,
    get_all_available_models,
)
from ii_agent.server.credits.service import (
    has_sufficient_credits,
    deduct_user_credits,
)
from ii_agent.server.chat.models import SessionMetadata
from ii_agent.core.config.llm_config import APITypes

logger = logging.getLogger(__name__)


class ChatService:
    """Service for managing chat conversations."""

    # Pricing per 1K tokens for different models
    PRICING = {
        "gpt-4": 0.03,
        "gpt-3.5-turbo": 0.002,
        "claude-3-opus-20240229": 0.015,
        "claude-3-sonnet-20240229": 0.003,
        "gemini-pro": 0.001,
    }

    @classmethod
    async def create_chat_session(
        cls, *, db_session: AsyncSession, user_id: str, model_id: str
    ) -> SessionMetadata:
        """
        Create a new chat session.

        Args:
            db_session: Database session
            user_id: User ID who owns the session
            model_id: LLM model ID for this session

        Returns:
            SessionMetadata: Session metadata with id, name, created_at, etc.
        """
        session_id = str(uuid.uuid4())
        created_at = datetime.now(timezone.utc)

        session = Session(
            id=session_id,
            user_id=user_id,
            name="Untitled",
            status="active",
            agent_type="chat",
            created_at=created_at,
            updated_at=created_at,
        )
        db_session.add(session)
        await db_session.commit()
        await db_session.refresh(session)

        logger.info(f"Created chat session {session_id} for user {user_id}")

        return SessionMetadata(
            session_id=session_id,
            name=session.name,
            status="active",
            agent_type="chat",
            model_id=model_id,
            created_at=created_at.isoformat(),
        )

    @classmethod
    async def validate_session_access(
        cls, *, db_session: AsyncSession, session_id: str, user_id: str
    ) -> None:
        """
        Validate that user has access to session.

        Args:
            db_session: Database session
            session_id: Session ID to validate
            user_id: User ID to check access for

        Raises:
            ValueError: If session not found or access denied
        """
        result = await db_session.execute(
            select(Session).where(
                Session.id == session_id,
                Session.user_id == user_id,
            )
        )
        session = result.scalar_one_or_none()

        if not session:
            raise ValueError("Session not found or access denied")

    @classmethod
    async def check_sufficient_credits(
        cls, *, db_session: AsyncSession, user_id: str
    ) -> bool:
        """
        Check if user has sufficient credits.

        Args:
            db_session: Database session
            user_id: User ID to check

        Returns:
            True if user has sufficient credits, False otherwise
        """
        return await has_sufficient_credits(
            db_session=db_session,
            user_id=user_id,
            amount=0.01,  # Minimum credit threshold
        )

    @classmethod
    async def validate_model_for_chat(
        cls, *, db_session: AsyncSession, model_id: str, user_id: str
    ) -> None:
        """
        Validate that the model exists and is available for chat.

        Supports: OpenAI, Gemini, and other LiteLLM-compatible models.

        Args:
            db_session: Database session
            model_id: Model ID to validate
            user_id: User ID

        Raises:
            ValueError: If model is not found
        """
        # Get all available models
        # TODO: Optimize this
        all_models = await get_all_available_models(
            user_id=user_id, db_session=db_session
        )

        # Find the requested model
        model_info = next((m for m in all_models.models if m.id == model_id), None)

        if not model_info:
            raise ValueError(f"Model not found: {model_id}")

        # Model exists and is available for chat
        # LiteLLM handles the API differences automatically

    @classmethod
    async def get_llm_config(
        cls, *, db_session: AsyncSession, model_id: str, user_id: str
    ):
        """
        Get LLM config for a model (from user settings or system config).

        Args:
            db_session: Database session
            model_id: Model ID
            user_id: User ID

        Returns:
            LLMConfig

        Raises:
            ValueError: If model not found
        """
        # Try to get from user settings first
        try:
            return await get_user_llm_config(
                model_id=model_id,
                user_id=user_id,
                db_session=db_session,
            )
        except ValueError:
            # Fall back to system config
            return get_system_llm_config(model_id=model_id)

    @classmethod
    async def stream_chat_response(
        cls,
        *,
        db_session: AsyncSession,
        session_id: str,
        user_id: str,
        content: str,
        model_id: str,
    ) -> AsyncIterator[Dict]:
        """
        Stream chat response using new architecture with tool execution loop.

        This implements an outer loop that:
        1. Calls LLM with current message history
        2. Streams LLM response to frontend
        3. If finish_reason == "tool_use", executes tools locally
        4. Streams tool results to frontend
        5. Adds tool results to message history
        6. Loops back to step 1 with updated history
        7. Exits when finish_reason != "tool_use"
        """
        from ii_agent.server.chat.message_service import MessageService
        from ii_agent.server.chat.content_parts import (
            TextContent,
            MessageRole,
            ToolResult,
        )
        from ii_agent.server.chat.provider_interface import EventType, FinishReason
        from ii_agent.server.chat.context_manager import ContextWindowManager
        from sqlalchemy import select

        # Get session for context window check
        result = await db_session.execute(
            select(Session).where(Session.id == session_id)
        )
        session = result.scalar_one()

        # Check if summarization is needed
        await ContextWindowManager.check_and_summarize(
            db_session=db_session, session=session, model_id=model_id
        )

        # Get conversation history with summary filtering
        messages = await ContextWindowManager.get_messages_with_summary(
            db_session=db_session,
            session_id=session_id,
            summary_message_id=session.summary_message_id,
        )

        # Create user message with TextContent part
        user_text_part = TextContent(text=content)
        user_message = await MessageService.create_message(
            db_session=db_session,
            session_id=session_id,
            role=MessageRole.USER,
            parts=[user_text_part],
        )

        # Add to messages list
        messages.append(user_message)

        # Get LLM config and create provider
        llm_config = await cls.get_llm_config(
            db_session=db_session, model_id=model_id, user_id=user_id
        )
        provider = LLMProviderFactory.create_provider(llm_config)

        try:
            # Outer loop: continue until no more tool calls
            while True:
                # Accumulate parts for this assistant turn
                accumulated_parts = []
                accumulated_response = None
                accumulated_thinking = ""

                # Stream LLM response
                async for event in provider.stream(messages=messages):
                    # Convert ProviderEvent to dict for SSE
                    if event.type == EventType.CONTENT_START:
                        yield {"type": "content_start"}

                    elif event.type == EventType.CONTENT_DELTA:
                        yield {"type": "content_delta", "content": event.content}

                    elif event.type == EventType.CONTENT_STOP:
                        yield {"type": "content_stop"}

                    elif event.type == EventType.THINKING_DELTA:
                        accumulated_thinking += event.thinking
                        yield {
                            "type": "thinking_delta",
                            "thinking": event.thinking,
                            "signature": event.signature if event.signature else None,
                        }

                    elif event.type == EventType.TOOL_USE_START:
                        yield {"type": "tool_use_start", "tool_call": event.tool_call}

                    elif event.type == EventType.TOOL_USE_DELTA:
                        yield {"type": "tool_use_delta", "tool_call": event.tool_call}

                    elif event.type == EventType.TOOL_USE_STOP:
                        yield {"type": "tool_use_stop", "tool_call": event.tool_call}

                    elif event.type == EventType.COMPLETE:
                        # Store the complete response
                        accumulated_response = event.response

                        # Build ContentParts from ProviderResponse
                        if accumulated_thinking:
                            from ii_agent.server.chat.content_parts import (
                                ReasoningContent,
                            )

                            accumulated_parts.append(
                                ReasoningContent(thinking=accumulated_thinking)
                            )

                        if event.response.content:
                            accumulated_parts.append(
                                TextContent(text=event.response.content)
                            )

                        for tool_call in event.response.tool_calls:
                            accumulated_parts.append(tool_call)

                # Yield usage event for this LLM turn
                yield {
                    "type": "usage",
                    "usage": {
                        "input_tokens": accumulated_response.usage.input_tokens,
                        "output_tokens": accumulated_response.usage.output_tokens,
                        "cache_creation_tokens": accumulated_response.usage.cache_creation_tokens,
                        "cache_read_tokens": accumulated_response.usage.cache_read_tokens,
                    },
                }

                # Save assistant message with ContentParts
                assistant_message = await MessageService.create_message(
                    db_session=db_session,
                    session_id=session_id,
                    role=MessageRole.ASSISTANT,
                    parts=accumulated_parts,
                    model=model_id,
                )

                # Update session token counts
                await cls._update_session_tokens(
                    db_session=db_session,
                    session_id=session_id,
                    usage=accumulated_response.usage,
                )

                # Add assistant message to history
                messages.append(assistant_message)

                # Check if we need to execute tools
                if accumulated_response.finish_reason == FinishReason.TOOL_USE:
                    # Execute tools and collect results
                    tool_result_parts = []

                    for tool_call in accumulated_response.tool_calls:
                        # Execute tool (placeholder - actual implementation depends on tool registry)
                        tool_output, is_error = await cls._execute_tool(
                            tool_name=tool_call.name,
                            tool_input=tool_call.input,
                            session_id=session_id,
                        )

                        # Yield tool_result event to frontend
                        yield {
                            "type": "tool_result",
                            "tool_call_id": tool_call.id,
                            "name": tool_call.name,
                            "output": tool_output,
                            "is_error": is_error,
                        }

                        # Create ToolResult ContentPart
                        tool_result_parts.append(
                            ToolResult(
                                tool_call_id=tool_call.id,
                                name=tool_call.name,
                                content=tool_output,
                                is_error=is_error,
                            )
                        )

                    # Save tool results as a message
                    tool_results_message = await MessageService.create_message(
                        db_session=db_session,
                        session_id=session_id,
                        role=MessageRole.TOOL,
                        parts=tool_result_parts,
                    )

                    # Add tool results to history
                    messages.append(tool_results_message)
                    await db_session.commit()

                    # Continue loop - call LLM again with tool results
                    continue

                else:
                    # No more tool calls - exit loop
                    # Deduct credits for the entire conversation
                    total_tokens = (
                        accumulated_response.usage.input_tokens
                        + accumulated_response.usage.output_tokens
                    )
                    cost = cls._calculate_cost(model_id, total_tokens)
                    await deduct_user_credits(
                        db_session=db_session,
                        user_id=user_id,
                        amount=cost,
                        description=f"Chat message - {model_id}",
                    )

                    await db_session.commit()

                    # Send complete event
                    yield {
                        "type": "complete",
                        "message_id": assistant_message.id,
                        "finish_reason": accumulated_response.finish_reason.value,
                    }

                    # Exit loop
                    break

        except Exception as e:
            logger.error(f"Chat streaming error: {e}", exc_info=True)
            raise

    @classmethod
    async def get_message_history(
        cls,
        *,
        db_session: AsyncSession,
        session_id: str,
        limit: int = 50,
        before: Optional[str] = None,
    ) -> Tuple[List[ChatMessage], bool]:
        """
        Get message history with pagination.

        Args:
            db_session: Database session
            session_id: Session ID
            limit: Maximum number of messages to return
            before: Message ID to get messages before

        Returns:
            Tuple of (list of messages, has_more flag)
        """

        query = (
            select(ChatMessage)
            .where(ChatMessage.session_id == session_id)
            .order_by(ChatMessage.created_at.desc())
            .limit(limit + 1)
        )

        if before:
            # Get messages before a specific message ID
            before_msg = await db_session.get(ChatMessage, before)
            if before_msg:
                query = query.where(ChatMessage.created_at < before_msg.created_at)

        result = await db_session.execute(query)
        messages = list(result.scalars().all())

        # Check if there are more messages
        has_more = len(messages) > limit
        if has_more:
            messages = messages[:limit]

        # Reverse to get chronological order
        messages.reverse()

        return messages, has_more

    @classmethod
    async def clear_messages(cls, *, db_session: AsyncSession, session_id: str) -> int:
        """
        Clear all messages in a session.

        Args:
            db_session: Database session
            session_id: Session ID

        Returns:
            Number of messages deleted
        """

        result = await db_session.execute(
            delete(ChatMessage).where(ChatMessage.session_id == session_id)
        )
        await db_session.commit()

        return result.rowcount

    @classmethod
    async def stop_conversation(
        cls, *, db_session: AsyncSession, session_id: str
    ) -> Optional[str]:
        """
        Stop a conversation by updating session status to 'pause'.

        Args:
            db_session: Database session
            session_id: Session ID

        Returns:
            Last message ID if conversation has messages, None otherwise

        Raises:
            ValueError: If session not found
        """
        # Get the session
        result = await db_session.execute(
            select(Session).where(Session.id == session_id)
        )
        session = result.scalar_one_or_none()

        if not session:
            raise ValueError("Session not found")

        # Update session status to paused
        session.status = "pause"
        session.updated_at = datetime.now(timezone.utc)

        # Get last message ID
        last_msg_result = await db_session.execute(
            select(ChatMessage)
            .where(ChatMessage.session_id == session_id)
            .order_by(ChatMessage.created_at.desc())
            .limit(1)
        )
        last_message = last_msg_result.scalar_one_or_none()

        await db_session.commit()

        logger.info(f"Stopped conversation for session {session_id}")

        return str(last_message.id) if last_message else None

    @classmethod
    async def get_available_models(
        cls, *, db_session: AsyncSession, user_id: str
    ) -> List[Dict]:
        """
        Get list of available chat models for user.

        Supports: OpenAI, Gemini, and other LiteLLM-compatible models.

        Args:
            db_session: Database session
            user_id: User ID

        Returns:
            List of model information dictionaries for all available models
        """

        # Get all available models from user settings and system config
        all_models = await get_all_available_models(
            user_id=user_id, db_session=db_session
        )

        # Convert to chat model format
        result = []
        for model in all_models.models:
            # Get cost from pricing table or default
            cost = cls.PRICING.get(model.model, 0.005)

            # Map API type to provider name
            provider_map = {
                APITypes.OPENAI: "openai",
                APITypes.GEMINI: "gemini",
                APITypes.ANTHROPIC: "anthropic",
            }
            provider = provider_map.get(model.api_type, model.api_type.value)

            result.append(
                {
                    "id": model.id,
                    "name": model.model,
                    "provider": provider,
                    "cost_per_1k_tokens": cost,
                    "max_tokens": 8192,  # Default, could be enhanced per provider
                    "supports_streaming": True,
                }
            )

        return result

    @classmethod
    async def _get_conversation_history(
        cls, *, db_session: AsyncSession, session_id: str, limit: int
    ) -> List[ChatMessage]:
        """
        Get recent conversation history.

        Args:
            db_session: Database session
            session_id: Session ID
            limit: Maximum number of messages to return

        Returns:
            List of chat messages in chronological order
        """

        result = await db_session.execute(
            select(ChatMessage)
            .where(ChatMessage.session_id == session_id)
            .order_by(ChatMessage.created_at.desc())
            .limit(limit)
        )
        messages = list(result.scalars().all())
        messages.reverse()  # Chronological order

        return messages

    @classmethod
    def _calculate_cost(cls, model_id: str, tokens: int) -> float:
        """
        Calculate credit cost for token usage.

        Args:
            model_id: Model ID
            tokens: Number of tokens used

        Returns:
            Cost in credits
        """

        rate = cls.PRICING.get(model_id, 0.005)  # Default rate
        return (tokens / 1000) * rate

    @classmethod
    async def _update_session_tokens(
        cls, *, db_session: AsyncSession, session_id: str, usage: "TokenUsage"
    ) -> None:
        """Update session token counts."""
        from sqlalchemy import update

        await db_session.execute(
            update(Session)
            .where(Session.id == session_id)
            .values(
                prompt_tokens=Session.prompt_tokens + usage.input_tokens,
                completion_tokens=Session.completion_tokens + usage.output_tokens,
            )
        )

    @classmethod
    async def _execute_tool(
        cls, *, tool_name: str, tool_input: str, session_id: str
    ) -> tuple[str, bool]:
        """
        Execute a tool and return its output.

        Args:
            tool_name: Name of the tool to execute
            tool_input: JSON string of tool input parameters
            session_id: Session ID for context

        Returns:
            Tuple of (tool_output: str, is_error: bool)

        Note:
            This is a placeholder implementation. Actual implementation should:
            1. Look up tool in a registry (e.g., WebSearch, FileRead, Calculator)
            2. Parse tool_input JSON
            3. Execute tool with parsed parameters
            4. Handle errors and return formatted output
        """
        import json

        try:
            # Parse tool input
            params = json.loads(tool_input)

            # Placeholder: actual implementation would dispatch to tool registry
            # For example:
            # tool_registry = get_tool_registry()
            # tool = tool_registry.get(tool_name)
            # result = await tool.execute(params)
            # return result.output, result.is_error

            # For now, return mock result
            output = f"[Tool {tool_name} executed with params: {params}]"
            return output, False

        except Exception as e:
            logger.error(f"Tool execution error: {e}", exc_info=True)
            return f"Error executing tool: {str(e)}", True
