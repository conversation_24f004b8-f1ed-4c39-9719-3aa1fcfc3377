"""Chat API router with SSE streaming support."""

import json
import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse

from ii_agent.server.api.deps import DBSession
from ii_agent.server.api.deps import CurrentUser
from ii_agent.server.chat.models import (
    ChatMessageRequest,
    ChatMessageResponse,
    MessageHistoryResponse,
    ClearHistoryResponse,
    ModelsListResponse,
    ModelInfo,
    StopConversationRequest,
    StopConversationResponse,
)
from ii_agent.server.chat.service import ChatService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/chat", tags=["chat"])


@router.post("/conversations")
async def send_chat_message(
    request: ChatMessageRequest,
    current_user: CurrentUser,
    db_session: DBSession,
):
    """
    Send a chat message with automatic session creation or reuse existing session.

    If request.session_id is provided, reuses that session.
    Otherwise, creates a new session automatically.

    Returns SSE stream with two event types:

    **Event: delta** (streaming content chunks)
    - delta_type: "content" | "reasoning"
    - data: string chunk

    **Event: message** (metadata and control events)
    - event: "session_created" - New session created (only if new session)
      - data: {session_id, name, status, agent_type, model_id, created_at}
    - event: "stream_start" - Message started
      - data: {session_id, model_id}
    - event: "tool_calls" - Tool/function calls
      - data: array of tool call objects
    - event: "function_call" - Legacy function call
      - data: {name, arguments}
    - event: "usage" - Token usage statistics
      - data: {completion_tokens, prompt_tokens, total_tokens, ...}
    - event: "stream_complete" - Stream finished
      - data: {message_id, message, usage, tokens, elapsed_ms}
    - event: "done" - All events sent
    - event: "error" - Error occurred
      - data: {error, code}
    """

    # Validate model exists and is available
    try:
        await ChatService.validate_model_for_chat(
            db_session=db_session,
            model_id=request.model_id,
            user_id=str(current_user.id),
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # Check credits
    has_credits = await ChatService.check_sufficient_credits(
        db_session=db_session, user_id=str(current_user.id)
    )
    if not has_credits:
        raise HTTPException(status_code=402, detail="Insufficient credits")

    # Use existing session or create new one
    session_metadata = None
    session_id = None

    if request.session_id:
        # Use existing session
        session_id = str(request.session_id)

        # Validate user has access to this session
        try:
            await ChatService.validate_session_access(
                db_session=db_session,
                session_id=session_id,
                user_id=str(current_user.id),
            )
            logger.info(
                f"Reusing existing session {session_id} for user {current_user.id}"
            )
        except ValueError as e:
            raise HTTPException(status_code=404, detail=str(e))
    else:
        # Create new session
        try:
            session_metadata = await ChatService.create_chat_session(
                db_session=db_session,
                user_id=str(current_user.id),
                model_id=request.model_id,
            )
            session_id = session_metadata.session_id
            logger.info(f"Created new session {session_id} for user {current_user.id}")
        except Exception as e:
            logger.error(f"Failed to create session: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to create session")

    async def event_generator():
        """Generate SSE events from provider stream following new SSE contract."""
        import time

        start_time = time.time()

        try:
            # Send session created event only if this is a new session
            if session_metadata:
                session_event = {
                    "status": "created",
                    "session_id": session_metadata.session_id,
                    "name": session_metadata.name,
                    "agent_type": session_metadata.agent_type,
                    "model_id": session_metadata.model_id,
                    "created_at": session_metadata.created_at,
                }
                yield f"event: session\ndata: {json.dumps(session_event)}\n\n"

            # Stream response from provider
            async for event in ChatService.stream_chat_response(
                db_session=db_session,
                session_id=session_id,
                user_id=str(current_user.id),
                content=request.content,
                model_id=request.model_id,
            ):
                event_type = event.get("type")

                # Content events (start/delta/stop)
                if event_type == "content_start":
                    yield f"event: content\ndata: {json.dumps({'status': 'start'})}\n\n"

                elif event_type == "content_delta":
                    content_event = {"status": "delta", "delta": event.get("content")}
                    yield f"event: content\ndata: {json.dumps(content_event)}\n\n"

                elif event_type == "content_stop":
                    yield f"event: content\ndata: {json.dumps({'status': 'stop'})}\n\n"

                # Thinking events (delta-only, no start/stop)
                elif event_type == "thinking_delta":
                    thinking_event = {"status": "delta", "delta": event.get("thinking")}
                    # Include signature if present (for o1 models)
                    if event.get("signature"):
                        thinking_event["signature"] = event.get("signature")
                    yield f"event: thinking\ndata: {json.dumps(thinking_event)}\n\n"

                # Tool call events (start/delta/stop)
                elif event_type == "tool_use_start":
                    tool_call = event.get("tool_call", {})
                    tool_event = {
                        "status": "start",
                        "id": tool_call.id
                        if hasattr(tool_call, "id")
                        else tool_call.get("id"),
                        "name": tool_call.name
                        if hasattr(tool_call, "name")
                        else tool_call.get("name"),
                        "type": tool_call.type
                        if hasattr(tool_call, "type")
                        else tool_call.get("type", "function"),
                    }
                    yield f"event: tool_call\ndata: {json.dumps(tool_event)}\n\n"

                elif event_type == "tool_use_delta":
                    tool_call = event.get("tool_call", {})
                    tool_event = {
                        "status": "delta",
                        "id": tool_call.id
                        if hasattr(tool_call, "id")
                        else tool_call.get("id"),
                        "delta": tool_call.input
                        if hasattr(tool_call, "input")
                        else tool_call.get("input", ""),  # Partial JSON
                    }
                    yield f"event: tool_call\ndata: {json.dumps(tool_event)}\n\n"

                elif event_type == "tool_use_stop":
                    tool_call = event.get("tool_call", {})
                    tool_event = {
                        "status": "stop",
                        "id": tool_call.id
                        if hasattr(tool_call, "id")
                        else tool_call.get("id"),
                        "name": tool_call.name
                        if hasattr(tool_call, "name")
                        else tool_call.get("name"),
                        "input": tool_call.input
                        if hasattr(tool_call, "input")
                        else tool_call.get("input"),  # Complete JSON
                    }
                    yield f"event: tool_call\ndata: {json.dumps(tool_event)}\n\n"

                # Tool result events (from backend execution)
                elif event_type == "tool_result":
                    result_event = {
                        "status": "info",
                        "tool_call_id": event.get("tool_call_id"),
                        "name": event.get("name"),
                        "output": event.get("output"),
                        "is_error": event.get("is_error", False),
                    }
                    yield f"event: tool_result\ndata: {json.dumps(result_event)}\n\n"

                # Usage events (per LLM turn)
                elif event_type == "usage":
                    usage = event.get("usage", {})
                    usage_event = {
                        "status": "info",
                        "input_tokens": usage.get("input_tokens", 0),
                        "output_tokens": usage.get("output_tokens", 0),
                        "cache_creation_tokens": usage.get("cache_creation_tokens", 0),
                        "cache_read_tokens": usage.get("cache_read_tokens", 0),
                        "total_tokens": usage.get("input_tokens", 0)
                        + usage.get("output_tokens", 0),
                    }
                    yield f"event: usage\ndata: {json.dumps(usage_event)}\n\n"

                # Complete event (final - only sent when loop exits)
                elif event_type == "complete":
                    elapsed_ms = int((time.time() - start_time) * 1000)
                    complete_event = {
                        "status": "done",
                        "message_id": event.get("message_id"),
                        "finish_reason": event.get("finish_reason", "end_turn"),
                        "elapsed_ms": elapsed_ms,
                    }
                    yield f"event: complete\ndata: {json.dumps(complete_event)}\n\n"

        except Exception as e:
            logger.error(f"Chat streaming error: {e}", exc_info=True)
            error_event = {
                "status": "error",
                "error": str(e),
                "code": "streaming_error",
            }
            yield f"event: error\ndata: {json.dumps(error_event)}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        },
    )


@router.post("/stop-conversation", response_model=StopConversationResponse)
async def stop_conversation(
    request: StopConversationRequest,
    current_user: CurrentUser,
    db_session: DBSession,
) -> StopConversationResponse:
    """
    Stop an ongoing conversation by updating session status to 'pause'.

    Args:
        request: Stop conversation request with session_id
        current_user: Current authenticated user
        db_session: Database session

    Returns:
        StopConversationResponse with success status and last message ID
    """
    session_id = str(request.session_id)

    # Validate session access
    try:
        await ChatService.validate_session_access(
            db_session=db_session,
            session_id=session_id,
            user_id=str(current_user.id),
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

    # Stop the conversation
    try:
        last_message_id = await ChatService.stop_conversation(
            db_session=db_session,
            session_id=session_id,
        )

        return StopConversationResponse(
            success=True,
            last_message_id=UUID(last_message_id) if last_message_id else None,
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to stop conversation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to stop conversation")


@router.get("/conversations/{session_id}", response_model=MessageHistoryResponse)
async def get_message_history(
    session_id: str,
    current_user: CurrentUser,
    db_session: DBSession,
    limit: int = Query(50, ge=1, le=200),
    before: Optional[str] = None,
) -> MessageHistoryResponse:
    """Get conversation history for a session."""

    try:
        # Validate session access
        await ChatService.validate_session_access(
            db_session=db_session,
            session_id=session_id,
            user_id=str(current_user.id),
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

    # Get messages
    messages, has_more = await ChatService.get_message_history(
        db_session=db_session,
        session_id=session_id,
        limit=limit,
        before=before,
    )

    # Convert to response model
    message_responses = [
        ChatMessageResponse(
            id=str(msg.id),
            role=msg.role,
            message=msg.content,  # Full message object
            usage=msg.usage,  # Usage statistics
            tokens=msg.tokens,
            model=msg.model,
            created_at=msg.created_at,
        )
        for msg in messages
    ]

    return MessageHistoryResponse(
        messages=message_responses,
        has_more=has_more,
        total_count=len(message_responses),
    )


@router.delete("/conversation/{session_id}", response_model=ClearHistoryResponse)
async def clear_conversation(
    session_id: str,
    current_user: CurrentUser,
    db_session: DBSession,
) -> ClearHistoryResponse:
    """Clear all messages in a conversation."""

    try:
        # Validate session access
        await ChatService.validate_session_access(
            db_session=db_session,
            session_id=session_id,
            user_id=str(current_user.id),
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

    # Clear messages
    deleted_count = await ChatService.clear_messages(
        db_session=db_session, session_id=session_id
    )

    return ClearHistoryResponse(
        success=True,
        deleted_count=deleted_count,
        message="Conversation cleared successfully",
    )


@router.get("/models", response_model=ModelsListResponse)
async def get_available_models(
    current_user: CurrentUser,
    db_session: DBSession,
) -> ModelsListResponse:
    """Get list of available chat models."""

    models_data = await ChatService.get_available_models(
        db_session=db_session, user_id=str(current_user.id)
    )

    models = [ModelInfo(**model) for model in models_data]

    return ModelsListResponse(models=models)
